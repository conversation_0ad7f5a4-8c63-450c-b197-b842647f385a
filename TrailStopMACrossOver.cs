//
// Copyright (C) 2024, NinjaTrader LLC <www.ninjatrader.com>.
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component with each release.
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
	public class TrailStopMACrossOver : Strategy
	{
		private SMA smaFast;
		private SMA smaSlow;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"MA CrossOver strategy with trailing stop functionality";
				Name										= "TrailStopMACrossOver";
				Calculate									= Calculate.OnPriceChange;
				EntriesPerDirection							= 1;
				EntryHandling								= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy				= true;
				ExitOnSessionCloseSeconds					= 30;
				IsFillLimitOnTouch							= false;
				MaximumBarsLookBack							= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution							= OrderFillResolution.Standard;
				Slippage									= 0;
				StartBehavior								= StartBehavior.WaitUntilFlat;
				TimeInForce									= TimeInForce.Gtc;
				TraceOrders									= false;
				RealtimeErrorHandling						= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling							= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade							= 20;
				// Disable this property for performance gains in Strategy Analyzer optimizations
				// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				Fast										= 10;
				Slow										= 25;
				TrailTicks									= 10;
			}
			else if (State == State.Configure)
			{
				// Set trailing stop
				SetTrailStop(@"", CalculationMode.Ticks, TrailTicks, false);
			}
			else if (State == State.DataLoaded)
			{
				smaFast = this.SMA(Fast);
				smaSlow = this.SMA(Slow);

				smaFast.Plots[0].Brush = Brushes.Goldenrod;
				smaSlow.Plots[0].Brush = Brushes.SeaGreen;

				AddChartIndicator(smaFast);
				AddChartIndicator(smaSlow);
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBar < BarsRequiredToTrade)
				return;

			if (BarsInProgress != 0) 
				return;

			// MA CrossOver logic for entries
			if (CrossAbove(smaFast, smaSlow, 1))
			{
				EnterLong(Convert.ToInt32(DefaultQuantity), @"");
			}
			else if (CrossBelow(smaFast, smaSlow, 1))
			{
				EnterShort(Convert.ToInt32(DefaultQuantity), @"");
			}
		}

		#region Properties
		[Range(1, int.MaxValue)]
		[Display(Name = "Fast", Order = 0, GroupName = "Parameters")]
		public int Fast
		{ get; set; }

		[Range(1, int.MaxValue)]
		[Display(Name = "Slow", Order = 1, GroupName = "Parameters")]
		public int Slow
		{ get; set; }

		[Range(1, int.MaxValue)]
		[Display(Name = "TrailTicks", Order = 2, GroupName = "Parameters")]
		public int TrailTicks
		{ get; set; }
		#endregion
	}
}
