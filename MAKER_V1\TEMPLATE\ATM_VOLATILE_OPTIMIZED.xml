<?xml version="1.0" encoding="utf-8"?>
<NinjaTrader>
  <AtmStrategy xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <IsVisible>true</IsVisible>
    <AreLinesConfigurable>true</AreLinesConfigurable>
    <ArePlotsConfigurable>true</ArePlotsConfigurable>
    <BarsToLoad>0</BarsToLoad>
    <DisplayInDataBox>true</DisplayInDataBox>
    <From>2099-12-01T00:00:00</From>
    <Panel>0</Panel>
    <ScaleJustification>Right</ScaleJustification>
    <ShowTransparentPlotsInDataBox>false</ShowTransparentPlotsInDataBox>
    <To>1800-01-01T00:00:00</To>
    <Calculate>OnBarClose</Calculate>
    <Displacement>0</Displacement>
    <IsAutoScale>true</IsAutoScale>
    <IsDataSeriesRequired>false</IsDataSeriesRequired>
    <IsOverlay>false</IsOverlay>
    <Lines />
    <MaximumBarsLookBack>TwoHundredFiftySix</MaximumBarsLookBack>
    <n>AtmStrategy</n>
    <Plots />
    <SelectedValueSeries>0</SelectedValueSeries>
    <BarsRequiredToTrade>0</BarsRequiredToTrade>
    <Category>Atm</Category>
    <ConnectionLossHandling>KeepRunning</ConnectionLossHandling>
    <DaysToLoad>1</DaysToLoad>
    <DefaultQuantity>2</DefaultQuantity>
    <DisconnectDelaySeconds>0</DisconnectDelaySeconds>
    <EntriesPerDirection>1</EntriesPerDirection>
    <EntryHandling>AllEntries</EntryHandling>
    <ExitOnSessionCloseSeconds>0</ExitOnSessionCloseSeconds>
    <IncludeCommission>false</IncludeCommission>
    <IsAggregated>false</IsAggregated>
    <IsExitOnSessionCloseStrategy>false</IsExitOnSessionCloseStrategy>
    <IsFillLimitOnTouch>false</IsFillLimitOnTouch>
    <IsOptimizeDataSeries>false</IsOptimizeDataSeries>
    <IsStableSession>false</IsStableSession>
    <IsTickReplay>false</IsTickReplay>
    <IsTradingHoursBreakLineVisible>false</IsTradingHoursBreakLineVisible>
    <IsWaitUntilFlat>false</IsWaitUntilFlat>
    <NumberRestartAttempts>0</NumberRestartAttempts>
    <OptimizationPeriod>10</OptimizationPeriod>
    <OrderFillResolution>High</OrderFillResolution>
    <OrderFillResolutionType>Tick</OrderFillResolutionType>
    <OrderFillResolutionValue>1</OrderFillResolutionValue>
    <RestartsWithinMinutes>0</RestartsWithinMinutes>
    <SetOrderQuantity>Strategy</SetOrderQuantity>
    <Slippage>0</Slippage>
    <StartBehavior>AdoptAccountPosition</StartBehavior>
    <StopTargetHandling>PerEntryExecution</StopTargetHandling>
    <SupportsOptimizationGraph>false</SupportsOptimizationGraph>
    <TestPeriod>28</TestPeriod>
    <TradingHoursSerializable />
    <Gtd>1800-01-01T00:00:00</Gtd>
    <Template>ATM_VOLATILE_OPTIMIZED</Template>
    <TimeInForce>Gtc</TimeInForce>
    <AtmSelector>4a39150a74934fba84b2165288418faa</AtmSelector>
    <OnBehalfOf />
    <ReverseAtStopStrategyId>-1</ReverseAtStopStrategyId>
    <ReverseAtTargetStrategyId>-1</ReverseAtTargetStrategyId>
    <ShadowStrategyStrategyId>-1</ShadowStrategyStrategyId>
    <ShadowTemplate />
    <Brackets>
      <Bracket>
        <Quantity>3</Quantity>
        <StopLoss>35</StopLoss>
        <StopStrategy>
          <AutoBreakEvenPlus>2</AutoBreakEvenPlus>
          <AutoBreakEvenProfitTrigger>15</AutoBreakEvenProfitTrigger>
          <AutoTrailSteps>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>20</ProfitTrigger>
              <StopLoss>12</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>35</ProfitTrigger>
              <StopLoss>20</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>50</ProfitTrigger>
              <StopLoss>35</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>70</ProfitTrigger>
              <StopLoss>50</StopLoss>
            </AutoTrailStep>
          </AutoTrailSteps>
          <IsSimStopEnabled>false</IsSimStopEnabled>
          <VolumeTrigger>0</VolumeTrigger>
          <Template>Stop Strategy 1</Template>
        </StopStrategy>
        <Target>50</Target>
      </Bracket>
      <Bracket>
        <Quantity>2</Quantity>
        <StopLoss>40</StopLoss>
        <StopStrategy>
          <AutoBreakEvenPlus>2</AutoBreakEvenPlus>
          <AutoBreakEvenProfitTrigger>20</AutoBreakEvenProfitTrigger>
          <AutoTrailSteps>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>30</ProfitTrigger>
              <StopLoss>18</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>45</ProfitTrigger>
              <StopLoss>30</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>65</ProfitTrigger>
              <StopLoss>45</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>85</ProfitTrigger>
              <StopLoss>65</StopLoss>
            </AutoTrailStep>
          </AutoTrailSteps>
          <IsSimStopEnabled>false</IsSimStopEnabled>
          <VolumeTrigger>0</VolumeTrigger>
          <Template>Stop Strategy 1</Template>
        </StopStrategy>
        <Target>75</Target>
      </Bracket>
      <Bracket>
        <Quantity>1</Quantity>
        <StopLoss>45</StopLoss>
        <StopStrategy>
          <AutoBreakEvenPlus>2</AutoBreakEvenPlus>
          <AutoBreakEvenProfitTrigger>25</AutoBreakEvenProfitTrigger>
          <AutoTrailSteps>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>35</ProfitTrigger>
              <StopLoss>22</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>50</ProfitTrigger>
              <StopLoss>35</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>75</ProfitTrigger>
              <StopLoss>50</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>100</ProfitTrigger>
              <StopLoss>75</StopLoss>
            </AutoTrailStep>
            <AutoTrailStep>
              <Frequency>1</Frequency>
              <ProfitTrigger>125</ProfitTrigger>
              <StopLoss>100</StopLoss>
            </AutoTrailStep>
          </AutoTrailSteps>
          <IsSimStopEnabled>false</IsSimStopEnabled>
          <VolumeTrigger>0</VolumeTrigger>
          <Template>Stop Strategy 1</Template>
        </StopStrategy>
        <Target>125</Target>
      </Bracket>
    </Brackets>
    <CalculationMode>Ticks</CalculationMode>
    <ChaseLimit>0</ChaseLimit>
    <EntryQuantity>6</EntryQuantity>
    <InitialTickSize>0</InitialTickSize>
    <IsChase>false</IsChase>
    <IsChaseIfTouched>false</IsChaseIfTouched>
    <IsTargetChase>false</IsTargetChase>
    <ReverseAtStop>false</ReverseAtStop>
    <ReverseAtTarget>false</ReverseAtTarget>
    <UseMitForProfit>false</UseMitForProfit>
    <UseStopLimitForStopLossOrders>false</UseStopLimitForStopLossOrders>
  </AtmStrategy>
</NinjaTrader>