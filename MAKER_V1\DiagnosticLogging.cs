using System;
using System.Text;
using System.Collections.Generic;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Strategies;
using NinjaTrader.Cbi;

namespace NinjaTrader.NinjaScript.Strategies
{
    public partial class MAKER_V1 : Strategy
    {
        // Diagnostic method to log detailed information about entry attempts
        private void LogEntryAttempt(string direction)
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"===== {Time[0]} ENTRY ATTEMPT DIAGNOSTIC LOG ({direction}) =====");

                // ATM Template Information
                sb.AppendLine($"ATM Template: {AtmStrategyTemplate}");
                sb.AppendLine($"Default Quantity: {DefaultQuantity}");

                // Account Information
                double availableMargin = Account.Get(AccountItem.ExcessInitialMargin, Currency.UsDollar);
                double requiredMargin = GetEstimatedMarginRequirement() * DefaultQuantity;
                sb.AppendLine($"Available Margin: ${availableMargin:F2}");
                sb.AppendLine($"Required Margin: ${requiredMargin:F2}");
                sb.AppendLine($"Margin Ratio: {(availableMargin / requiredMargin):F2}x");

                // Signal Information
                sb.AppendLine($"RSI Value: {rsi[0]:F2} (Overbought: {OverboughtThreshold}, Oversold: {OversoldThreshold})");
                sb.AppendLine($"Current Trend: {currentTrend}");
                sb.AppendLine($"Signal Quality: {signalQuality[0]:F2} (Always 100)");
                sb.AppendLine($"ADX Value: Removed (simplified strategy)");
                sb.AppendLine($"Volume Ratio: Removed (simplified strategy)");

                // Time Filter Information
                int currentHour = Time[0].Hour;
                DayOfWeek currentDay = Time[0].DayOfWeek;
                sessionIterator.GetNextSession(Time[0], false);
                DateTime sessionBegin = sessionIterator.ActualSessionBegin;
                DateTime sessionEnd = sessionIterator.ActualSessionEnd;
                sb.AppendLine($"Current Time: {Time[0].ToString("HH:mm:ss")} ({currentDay})");
                sb.AppendLine($"Session: {sessionBegin.ToString("HH:mm")} - {sessionEnd.ToString("HH:mm")}");
                sb.AppendLine($"Minutes From Session Start: {(Time[0] - sessionBegin).TotalMinutes:F1}");
                sb.AppendLine($"Minutes To Session End: {(sessionEnd - Time[0]).TotalMinutes:F1}");

                // Hourly Performance
                if (hourlyPerformance.ContainsKey(currentHour))
                {
                    var hourStats = hourlyPerformance[currentHour];
                    sb.AppendLine($"Hour {currentHour} Performance: {hourStats.WinRate:F1}% win rate over {hourStats.TotalTrades} trades");
                }

                // Risk Management Information
                sb.AppendLine($"Risk Management: DISABLED (simplified strategy)");

                // Daily P&L Information
                if (dailyStartingBalance > 0)
                {
                    double currentBalance = Account.Get(AccountItem.CashValue, Currency.UsDollar);
                    double dailyPnLPercent = ((currentBalance - dailyStartingBalance) / dailyStartingBalance) * 100;
                    sb.AppendLine($"Daily P&L: {dailyPnLPercent:F2}%");
                }

                // Position Information
                sb.AppendLine($"Current Position: {Position.MarketPosition}");
                sb.AppendLine($"ATM Strategy ID: '{atmStrategyId}'");
                sb.AppendLine($"Order ID: '{orderId}'");

                // Entry Condition Summary
                bool trendAllowsEntry = false;
                if (direction == "LONG")
                {
                    trendAllowsEntry = !EnableTrendFilter ||
                                      (OnlyTradeWithTrend && currentTrend == TrendDirection.Up) ||
                                      (!OnlyTradeWithTrend && currentTrend != TrendDirection.Down);
                }
                else // SHORT
                {
                    trendAllowsEntry = !EnableTrendFilter ||
                                     (OnlyTradeWithTrend && currentTrend == TrendDirection.Down) ||
                                     (!OnlyTradeWithTrend && currentTrend != TrendDirection.Up);
                }

                // Time filters removed as requested
                bool timeAllowsEntry = true;

                // All filters removed as requested
                bool volumeAllowsEntry = true; // Volume filter removed
                bool qualityAllowsEntry = true; // Signal quality filter removed
                bool adxAllowsEntry = true; // ADX filter removed
                bool riskAllowsEntry = true; // Risk management removed

                // Entry Condition Summary
                sb.AppendLine("\nENTRY CONDITION SUMMARY:");
                sb.AppendLine($"Trend Filter: {(trendAllowsEntry ? "PASS" : "FAIL")} ({currentTrend})");
                sb.AppendLine($"Time Filter: REMOVED (simplified strategy)");
                sb.AppendLine($"Volume Filter: REMOVED (simplified strategy)");
                sb.AppendLine($"Quality Filter: REMOVED (simplified strategy)");
                sb.AppendLine($"ADX Filter: REMOVED (simplified strategy)");
                sb.AppendLine($"Risk Filter: REMOVED (simplified strategy)");
                bool allConditionsAllowEntry = trendAllowsEntry && timeAllowsEntry && volumeAllowsEntry && qualityAllowsEntry && adxAllowsEntry && riskAllowsEntry;
                sb.AppendLine($"OVERALL ENTRY DECISION: {(allConditionsAllowEntry ? "ALLOWED" : "BLOCKED")}");

                // Print the diagnostic log
                Print(sb.ToString());

                // Also log to file with appropriate category
                LogContext(LogLevel.Info, LogCategory.TRADE, $"Entry attempt ({direction}): {(allConditionsAllowEntry ? "ALLOWED" : "BLOCKED")}");

                // Log detailed entry conditions to file
                Dictionary<string, object> entryConditions = new Dictionary<string, object>
                {
                    { "Direction", direction },
                    { "Trend", trendAllowsEntry },
                    { "Time", timeAllowsEntry },
                    { "Volume", volumeAllowsEntry },
                    { "Quality", qualityAllowsEntry },
                    { "ADX", adxAllowsEntry },
                    { "Risk", riskAllowsEntry },
                    { "Overall", allConditionsAllowEntry }
                };
                // Log entry conditions using LogContext directly to avoid ambiguity
                StringBuilder conditionsStr = new StringBuilder();
                foreach (var kvp in entryConditions)
                {
                    string valueStr = kvp.Value is bool ? (bool)kvp.Value ? "Yes" : "No" : kvp.Value.ToString();
                    conditionsStr.Append($"{kvp.Key}={valueStr}, ");
                }
                if (conditionsStr.Length > 2) conditionsStr.Length -= 2;

                LogContext(LogLevel.Info, LogCategory.TRADE, $"Entry conditions for {direction}: {conditionsStr}");
            }
            catch (Exception ex)
            {
                LogContext(LogLevel.Error, LogCategory.SYSTEM, $"Error in LogEntryAttempt: {ex.Message}");
                Print($"Error in LogEntryAttempt: {ex.Message}");
            }
        }
    }
}