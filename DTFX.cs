using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;

namespace NinjaTrader.NinjaScript.Strategies.SamuraiJackDTFX
{
    using System.IO;

    public class DTFXAlgoZonesStrategySJ : Strategy
    {
        #region Structures & Enums
        private struct SwingPoint
        {
            public double Price;
            public int Bar; // Bar Index on its own time series

            public SwingPoint(double price, int bar)
            {
                Price = price;
                Bar = bar;
            }
            public bool IsValid() { return Bar >= 0; }
        }

        private class FibZone
        {
            public int StartBar;
            public int EndBar;
            public double Top;
            public double Bottom;
            public bool IsBullish;
            public bool IsValid;
            public Dictionary<double, double> FibLevels;
            public SwingPoint SourceSwing;
            public SwingPoint TargetSwing;
            public string DrawTag;

            public FibZone(int startBar, double top, double bottom, bool isBullish, SwingPoint sourceSwing, SwingPoint targetSwing, Dictionary<double, double> fibLevels, string tag)
            {
                StartBar = startBar;
                EndBar = startBar;
                Top = top;
                Bottom = bottom;
                IsBullish = isBullish;
                IsValid = true;
                FibLevels = fibLevels;
                SourceSwing = sourceSwing;
                TargetSwing = targetSwing;
                DrawTag = tag;
            }
        }

        public enum ZoneDisplayFilter
        {
            Both,
            BullishOnly,
            BearishOnly
        }

        public enum InvalidationTypeOption
        {
            Close,
            Wick
        }

        public enum LineStyleOption
        {
            Solid,
            Dash,
            Dot
        }
        #endregion

        #region Parameters & State Variables
        // Primary Chart State
        private SwingPoint lastTop = new SwingPoint(0, -1);
        private SwingPoint lastBottom = new SwingPoint(0, -1);
        private List<SwingPoint> swingHighs = new List<SwingPoint>();
        private List<SwingPoint> swingLows = new List<SwingPoint>();
        private int dir = 0; // 1 = looking for lows, -1 = looking for highs
        private int trendDir = 0; // 1 = bullish, -1 = bearish
        private bool bosUpCheck = false;
        private bool bosDownCheck = false;
        private List<FibZone> activeZones = new List<FibZone>();
        private FibZone latestZoneForTrading = null;
        private int zoneCounter = 0;
        private bool mssUp = false;
        private bool mssDown = false;

        // HTF (1-Hour) State
        private SwingPoint htfLastTop = new SwingPoint(0, -1);
        private SwingPoint htfLastBottom = new SwingPoint(0, -1);
        private List<SwingPoint> htfSwingHighs = new List<SwingPoint>();
        private List<SwingPoint> htfSwingLows = new List<SwingPoint>();
        private int htfDir = 0;
        private int htfTrendDir = 0; // 1 = Bull, -1 = Bear, 0 = Neutral
        private bool htfBosUpCheck = false;
        private bool htfBosDownCheck = false;
        private List<FibZone> htfActiveZones = new List<FibZone>();
        private int htfZoneCounter = 0;

        // Constants
        private const int PrimaryBarsIndex = 0;
        private const int HTFBarsIndex = 1;

        // Logging Flags (now configurable)
        private bool EnableDetailedLogging;
        private bool LogZoneManagement;
        private bool LogTradeExecution;

        // Parameters
        [NinjaScriptProperty]
        [Display(Name="Enable 1hr TF Filter", Order=1, GroupName="7. Multi-Timeframe")]
        public bool EnableHTFFilter { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable HTF Zones", Order=2, GroupName="7. Multi-Timeframe")]
        public bool EnableHTFZones { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Show Swing Points", Order=1, GroupName="1. Structure")]
        public bool ShowSwingPoints { get; set; }

        [NinjaScriptProperty]
        [Range(2, int.MaxValue)]
        [Display(Name="Structure Length", Order=2, GroupName="1. Structure")]
        public int StructureLength { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Show Last N Zones", Order=3, GroupName="2. Zones")]
        public int ZoneDispNum { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Display All Zones", Order=4, GroupName="2. Zones")]
        public bool DisplayAllZones { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Zone Display Filter", Order=5, GroupName="2. Zones")]
        public ZoneDisplayFilter ZoneFilter { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Extend Fibonacci Lines", Order=7, GroupName="2. Zones")]
        public bool ExtendZones { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Extend Zone Boxes", Order=8, GroupName="2. Zones")]
        public bool ExtendBoxes { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable Zone Invalidation", Order=10, GroupName="3. Invalidation")]
        public bool EnableZoneInvalidation { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Invalidation Type", Order=11, GroupName="3. Invalidation")]
        public InvalidationTypeOption InvalidationType { get; set; }

        // Fibonacci Levels
        [NinjaScriptProperty] [Display(Name="Fib 1 Enable", Order=1, GroupName="4. Fib Levels")] public bool F1Tog { get; set; }
        [NinjaScriptProperty] [Range(0, 2)] [Display(Name="Fib 1 Level", Order=2, GroupName="4. Fib Levels")] public double F1Lvl { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 2 Enable", Order=3, GroupName="4. Fib Levels")] public bool F2Tog { get; set; }
        [NinjaScriptProperty] [Range(0, 2)] [Display(Name="Fib 2 Level", Order=4, GroupName="4. Fib Levels")] public double F2Lvl { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 3 Enable", Order=5, GroupName="4. Fib Levels")] public bool F3Tog { get; set; }
        [NinjaScriptProperty] [Range(0, 2)] [Display(Name="Fib 3 Level", Order=6, GroupName="4. Fib Levels")] public double F3Lvl { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 4 Enable", Order=7, GroupName="4. Fib Levels")] public bool F4Tog { get; set; }
        [NinjaScriptProperty] [Range(0, 2)] [Display(Name="Fib 4 Level", Order=8, GroupName="4. Fib Levels")] public double F4Lvl { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 5 Enable", Order=9, GroupName="4. Fib Levels")] public bool F5Tog { get; set; }
        [NinjaScriptProperty] [Range(0, 2)] [Display(Name="Fib 5 Level", Order=10, GroupName="4. Fib Levels")] public double F5Lvl { get; set; }

        // Fib Line Styles
        [NinjaScriptProperty] [Display(Name="Fib 1 Style", Order=11, GroupName="4. Fib Levels")] public LineStyleOption F1Style { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 2 Style", Order=12, GroupName="4. Fib Levels")] public LineStyleOption F2Style { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 3 Style", Order=13, GroupName="4. Fib Levels")] public LineStyleOption F3Style { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 4 Style", Order=14, GroupName="4. Fib Levels")] public LineStyleOption F4Style { get; set; }
        [NinjaScriptProperty] [Display(Name="Fib 5 Style", Order=15, GroupName="4. Fib Levels")] public LineStyleOption F5Style { get; set; }

        // Colors
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 1 Bull Color", Order=16, GroupName="4. Fib Levels")] public Brush F1BullBrush { get; set; }
        [Browsable(false)] public string F1BullBrushSerialize { get { return Serialize.BrushToString(F1BullBrush); } set { F1BullBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 2 Bull Color", Order=17, GroupName="4. Fib Levels")] public Brush F2BullBrush { get; set; }
        [Browsable(false)] public string F2BullBrushSerialize { get { return Serialize.BrushToString(F2BullBrush); } set { F2BullBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 3 Bull Color", Order=18, GroupName="4. Fib Levels")] public Brush F3BullBrush { get; set; }
        [Browsable(false)] public string F3BullBrushSerialize { get { return Serialize.BrushToString(F3BullBrush); } set { F3BullBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 4 Bull Color", Order=19, GroupName="4. Fib Levels")] public Brush F4BullBrush { get; set; }
        [Browsable(false)] public string F4BullBrushSerialize { get { return Serialize.BrushToString(F4BullBrush); } set { F4BullBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 5 Bull Color", Order=20, GroupName="4. Fib Levels")] public Brush F5BullBrush { get; set; }
        [Browsable(false)] public string F5BullBrushSerialize { get { return Serialize.BrushToString(F5BullBrush); } set { F5BullBrush = Serialize.StringToBrush(value); } }

        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 1 Bear Color", Order=21, GroupName="4. Fib Levels")] public Brush F1BearBrush { get; set; }
        [Browsable(false)] public string F1BearBrushSerialize { get { return Serialize.BrushToString(F1BearBrush); } set { F1BearBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 2 Bear Color", Order=22, GroupName="4. Fib Levels")] public Brush F2BearBrush { get; set; }
        [Browsable(false)] public string F2BearBrushSerialize { get { return Serialize.BrushToString(F2BearBrush); } set { F2BearBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 3 Bear Color", Order=23, GroupName="4. Fib Levels")] public Brush F3BearBrush { get; set; }
        [Browsable(false)] public string F3BearBrushSerialize { get { return Serialize.BrushToString(F3BearBrush); } set { F3BearBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 4 Bear Color", Order=24, GroupName="4. Fib Levels")] public Brush F4BearBrush { get; set; }
        [Browsable(false)] public string F4BearBrushSerialize { get { return Serialize.BrushToString(F4BearBrush); } set { F4BearBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Fib 5 Bear Color", Order=25, GroupName="4. Fib Levels")] public Brush F5BearBrush { get; set; }
        [Browsable(false)] public string F5BearBrushSerialize { get { return Serialize.BrushToString(F5BearBrush); } set { F5BearBrush = Serialize.StringToBrush(value); } }

        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Structure Point Color", Order=1, GroupName="5. Style")] public Brush StructureBrush { get; set; }
        [Browsable(false)] public string StructureBrushSerialize { get { return Serialize.BrushToString(StructureBrush); } set { StructureBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Bullish Zone Color", Order=2, GroupName="5. Style")] public Brush BullZoneBrush { get; set; }
        [Browsable(false)] public string BullZoneBrushSerialize { get { return Serialize.BrushToString(BullZoneBrush); } set { BullZoneBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [XmlIgnore] [Display(Name="Bearish Zone Color", Order=3, GroupName="5. Style")] public Brush BearZoneBrush { get; set; }
        [Browsable(false)] public string BearZoneBrushSerialize { get { return Serialize.BrushToString(BearZoneBrush); } set { BearZoneBrush = Serialize.StringToBrush(value); } }
        [NinjaScriptProperty] [Range(0, 100)] [Display(Name="Zone Opacity %", Order=4, GroupName="5. Style")] public int ZoneOpacity { get; set; }
        [NinjaScriptProperty] [Range(1, 500)] [Display(Name="Look Forward Bars", Order=5, GroupName="5. Style")] public int LookForwardBars { get; set; }

        // Strategy Settings
        [NinjaScriptProperty]
        [Display(Name="Stop Loss Ticks", Order=1, GroupName="6. Strategy Settings")]
        public int StopLossTicks { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Trade Quantity", Order=2, GroupName="6. Strategy Settings")]
        public int TradeQuantity { get; set; }

        [NinjaScriptProperty]
        [Range(0.5, 10)]
        [Display(Name="Minimum Risk-Reward Ratio", Order=3, GroupName="6. Strategy Settings")]
        public double MinRiskRewardRatio { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable Trailing Stop", Order=4, GroupName="6. Strategy Settings")]
        public bool EnableTrailingStop { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Trailing Stop Ticks", Order=5, GroupName="6. Strategy Settings")]
        public int TrailingStopTicks { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Require Candle Confirmation", Order=6, GroupName="6. Strategy Settings")]
        public bool RequireCandleConfirmation { get; set; }

        [NinjaScriptProperty]
        [Range(1, 10)]
        [Display(Name="Confirmation Bars", Order=7, GroupName="6. Strategy Settings")]
        public int ConfirmationBars { get; set; }

        // Logging Settings
        [NinjaScriptProperty]
        [Display(Name="Enable Detailed Logging", Order=1, GroupName="8. Logging")]
        public bool EnableDetailedLoggingProp { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Log Zone Management", Order=2, GroupName="8. Logging")]
        public bool LogZoneManagementProp { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Log Trade Execution", Order=3, GroupName="8. Logging")]
        public bool LogTradeExecutionProp { get; set; }
        #endregion

        #region OnStateChange
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"DTFX Algo Zones Strategy with MTF and Enhanced Features";
                Name = "DTFXAlgoZonesStrategySJ_Enhanced";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 2; // Allow multiple entries
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = StructureLength * 3 + 20;

                // Default Parameter Values
                EnableHTFFilter = true;
                EnableHTFZones = false; // Default off, enable for confluence
                ShowSwingPoints = true;
                StructureLength = 10;
                ZoneDispNum = 3;
                DisplayAllZones = false;
                ZoneFilter = ZoneDisplayFilter.Both;
                ExtendZones = true;
                ExtendBoxes = true;
                EnableZoneInvalidation = true;
                InvalidationType = InvalidationTypeOption.Close;

                F1Tog = true; F1Lvl = 0.0; F1Style = LineStyleOption.Dot;
                F2Tog = true; F2Lvl = 0.382; F2Style = LineStyleOption.Dash;
                F3Tog = true; F3Lvl = 0.5; F3Style = LineStyleOption.Solid;
                F4Tog = true; F4Lvl = 0.618; F4Style = LineStyleOption.Dash;
                F5Tog = true; F5Lvl = 1.0; F5Style = LineStyleOption.Dot;

                F1BullBrush = Brushes.Lime; F2BullBrush = Brushes.Yellow; F3BullBrush = Brushes.White; F4BullBrush = Brushes.Yellow; F5BullBrush = Brushes.Lime;
                F1BearBrush = Brushes.Magenta; F2BearBrush = Brushes.Yellow; F3BearBrush = Brushes.White; F4BearBrush = Brushes.Yellow; F5BearBrush = Brushes.Magenta;

                StructureBrush = Brushes.Gray;
                BullZoneBrush = Brushes.Green;
                BearZoneBrush = Brushes.Red;
                ZoneOpacity = 20;
                LookForwardBars = 50;

                StopLossTicks = 5;
                TradeQuantity = 1;
                MinRiskRewardRatio = 1.0;
                EnableTrailingStop = false;
                TrailingStopTicks = 3;
                RequireCandleConfirmation = false;
                ConfirmationBars = 2;

                EnableDetailedLoggingProp = false;
                LogZoneManagementProp = true;
                LogTradeExecutionProp = true;
            }
            else if (State == State.Configure)
            {
                AddDataSeries(BarsPeriodType.Minute, 60); // 1-hour HTF
            }
            else if (State == State.DataLoaded)
            {
                try
                {
                    // Initialize State
                    swingHighs = new List<SwingPoint>();
                    swingLows = new List<SwingPoint>();
                    lastTop = new SwingPoint(0, -1);
                    lastBottom = new SwingPoint(0, -1);
                    dir = 0;
                    trendDir = 0;
                    bosUpCheck = false;
                    bosDownCheck = false;
                    activeZones = new List<FibZone>();
                    latestZoneForTrading = null;
                    zoneCounter = 0;
                    mssUp = false;
                    mssDown = false;

                    htfSwingHighs = new List<SwingPoint>();
                    htfSwingLows = new List<SwingPoint>();
                    htfLastTop = new SwingPoint(0, -1);
                    htfLastBottom = new SwingPoint(0, -1);
                    htfDir = 0;
                    htfTrendDir = 0;
                    htfBosUpCheck = false;
                    htfBosDownCheck = false;
                    htfActiveZones = new List<FibZone>();
                    htfZoneCounter = 0;

                    EnableDetailedLogging = EnableDetailedLoggingProp;
                    LogZoneManagement = LogZoneManagementProp;
                    LogTradeExecution = LogTradeExecutionProp;

                    if (BarsArray == null || BarsArray.Length < 2)
                    {
                        Print("DTFX ERROR: BarsArray not properly initialized.");
                    }

                    Print("DTFX Strategy loaded successfully");

                    // Adjust brush opacity and Freeze
                    BullZoneBrush = AdjustOpacity(BullZoneBrush, ZoneOpacity);
                    BearZoneBrush = AdjustOpacity(BearZoneBrush, ZoneOpacity);
                    StructureBrush?.Freeze();
                    BullZoneBrush?.Freeze();
                    BearZoneBrush?.Freeze();
                    F1BullBrush?.Freeze(); F2BullBrush?.Freeze(); F3BullBrush?.Freeze(); F4BullBrush?.Freeze(); F5BullBrush?.Freeze();
                    F1BearBrush?.Freeze(); F2BearBrush?.Freeze(); F3BearBrush?.Freeze(); F4BearBrush?.Freeze(); F5BearBrush?.Freeze();
                }
                catch (Exception ex)
                {
                    Print($"DTFX ERROR: Exception during initialization: {ex.Message}");
                }
            }
        }
        #endregion

        #region OnBarUpdate
        protected override void OnBarUpdate()
        {
            try
            {
                // Guard Clauses
                int requiredBars = Math.Max(BarsRequiredToTrade, StructureLength * 2 + 5);
                if (CurrentBars[PrimaryBarsIndex] < requiredBars || CurrentBars[HTFBarsIndex] < requiredBars)
                {
                    if (EnableDetailedLogging && CurrentBar == 219)
                    {
                        Print($"DTFX: Not enough bars at 219: Primary={CurrentBars[PrimaryBarsIndex]}, HTF={CurrentBars[HTFBarsIndex]}, Required={requiredBars}");
                    }
                    return;
                }

                if (!BarsArray[PrimaryBarsIndex].IsTickReplay && CurrentBars[PrimaryBarsIndex] < 1 ||
                    !BarsArray[HTFBarsIndex].IsTickReplay && CurrentBars[HTFBarsIndex] < 1)
                {
                    return;
                }

                // Handle Session Breaks
                if (Bars.IsFirstBarOfSession)
                {
                    LogMessage("SESSION", "New session started, checking swing point continuity");
                    // Optionally reset or validate swing points
                }

                // Process HTF Data
                if (BarsInProgress == HTFBarsIndex)
                {
                    CalculateHTFStructure();
                    return;
                }

                // Process Primary Chart
                int currentPrimaryBar = CurrentBars[PrimaryBarsIndex];

                // Structure Detection
                if (currentPrimaryBar < StructureLength * 2 + 5)
                {
                    if (EnableDetailedLogging && (CurrentBar % 50 == 0 || CurrentBar == 219))
                    {
                        Print($"DTFX: Not enough bars: {currentPrimaryBar} < {StructureLength * 2 + 5}");
                    }
                    return;
                }

                if (EnableDetailedLogging && CurrentBar == 219)
                {
                    Print($"DTFX: Primary Structure on bar 219: CurrentBars[PrimaryBarsIndex]={currentPrimaryBar}, StructureLength={StructureLength}");
                }

                double highestHigh = double.MinValue;
                double lowestLow = double.MaxValue;
                for (int i = 1; i <= StructureLength; i++)
                {
                    if (currentPrimaryBar - i < 0) break;
                    highestHigh = Math.Max(highestHigh, High[i]);
                    lowestLow = Math.Min(lowestLow, Low[i]);
                }

                if (highestHigh == double.MinValue || lowestLow == double.MaxValue)
                {
                    if (EnableDetailedLogging) Print("DTFX: Could not calculate valid high/low values");
                    return;
                }

                // Swing High Detection
                bool canCheckSwingHigh = dir >= 0 && currentPrimaryBar >= StructureLength + 2;
                bool isSwingHighCandidate = false;
                if (canCheckSwingHigh)
                {
                    try
                    {
                        isSwingHighCandidate = High[StructureLength] >= highestHigh;
                    }
                    catch (Exception ex)
                    {
                        Print($"DTFX ERROR: Error checking swing high: {ex.Message}");
                        isSwingHighCandidate = false;
                    }
                }

                if (canCheckSwingHigh && isSwingHighCandidate)
                {
                    int potentialTopBar = -1;
                    double potentialTopPrice = double.MinValue;
                    for (int i = 1; i <= StructureLength; i++)
                    {
                        if (currentPrimaryBar - i < 0) break;
                        try
                        {
                            if (High[i] > potentialTopPrice)
                            {
                                potentialTopPrice = High[i];
                                potentialTopBar = CurrentBar - i;
                            }
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error in swing high loop at i={i}: {ex.Message}");
                            break;
                        }
                    }

                    bool isValidSwingHigh = potentialTopBar != -1 && currentPrimaryBar >= StructureLength + 2;
                    if (isValidSwingHigh)
                    {
                        try
                        {
                            isValidSwingHigh = potentialTopPrice > High[StructureLength + 1];
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error comparing with previous bar: {ex.Message}");
                            isValidSwingHigh = false;
                        }
                    }

                    if (isValidSwingHigh && (potentialTopBar > lastTop.Bar || !lastTop.IsValid()))
                    {
                        if (potentialTopPrice > lastTop.Price || !lastTop.IsValid())
                        {
                            dir = -1;
                            lastTop = new SwingPoint(potentialTopPrice, potentialTopBar);
                            swingHighs.Add(lastTop);
                            swingHighs.Sort((a, b) => b.Bar.CompareTo(a.Bar));
                            if (swingHighs.Count > 10) swingHighs.RemoveAt(swingHighs.Count - 1);
                            bosUpCheck = true;

                            try
                            {
                                if (ShowSwingPoints)
                                    Draw.Dot(this, "Top_" + lastTop.Bar, true, 0, lastTop.Price + TickSize, StructureBrush);
                            }
                            catch (Exception ex)
                            {
                                Print($"DTFX ERROR: Error drawing swing high dot: {ex.Message}");
                            }

                            LogMessage("STRUCTURE", $"New Primary High: {lastTop.Price} at Bar {lastTop.Bar}");
                        }
                    }
                }

                // Swing Low Detection
                bool canCheckSwingLow = dir <= 0 && currentPrimaryBar >= StructureLength + 2;
                bool isSwingLowCandidate = false;
                if (canCheckSwingLow)
                {
                    try
                    {
                        isSwingLowCandidate = Low[StructureLength] <= lowestLow;
                    }
                    catch (Exception ex)
                    {
                        Print($"DTFX ERROR: Error checking swing low: {ex.Message}");
                        isSwingLowCandidate = false;
                    }
                }

                if (canCheckSwingLow && isSwingLowCandidate)
                {
                    int potentialBotBar = -1;
                    double potentialBotPrice = double.MaxValue;
                    for (int i = 1; i <= StructureLength; i++)
                    {
                        if (currentPrimaryBar - i < 0) break;
                        try
                        {
                            if (Low[i] < potentialBotPrice)
                            {
                                potentialBotPrice = Low[i];
                                potentialBotBar = CurrentBar - i;
                            }
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error in swing low loop at i={i}: {ex.Message}");
                            break;
                        }
                    }

                    bool isValidSwingLow = potentialBotBar != -1 && currentPrimaryBar >= StructureLength + 2;
                    if (isValidSwingLow)
                    {
                        try
                        {
                            isValidSwingLow = potentialBotPrice < Low[StructureLength + 1];
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error comparing with previous bar: {ex.Message}");
                            isValidSwingLow = false;
                        }
                    }

                    if (isValidSwingLow && (potentialBotBar > lastBottom.Bar || !lastBottom.IsValid()))
                    {
                        if (potentialBotPrice < lastBottom.Price || !lastBottom.IsValid())
                        {
                            dir = 1;
                            lastBottom = new SwingPoint(potentialBotPrice, potentialBotBar);
                            swingLows.Add(lastBottom);
                            swingLows.Sort((a, b) => b.Bar.CompareTo(a.Bar));
                            if (swingLows.Count > 10) swingLows.RemoveAt(swingLows.Count - 1);
                            bosDownCheck = true;

                            try
                            {
                                if (ShowSwingPoints)
                                    Draw.Dot(this, "Bot_" + lastBottom.Bar, true, 0, lastBottom.Price - TickSize, StructureBrush);
                            }
                            catch (Exception ex)
                            {
                                Print($"DTFX ERROR: Error drawing swing low dot: {ex.Message}");
                            }

                            LogMessage("STRUCTURE", $"New Primary Low: {lastBottom.Price} at Bar {lastBottom.Bar}");
                        }
                    }
                }

                // BOS/CHoCH Detection
                bool chochUp = false;
                bool chochDown = false;
                bool bosUp = false;
                bool bosDown = false;

                if (lastTop.IsValid() && lastBottom.IsValid() && currentPrimaryBar >= 3)
                {
                    try
                    {
                        bool crossedAboveTop = false;
                        bool crossedBelowBottom = false;
                        if (currentPrimaryBar >= 2)
                        {
                            double currentClose = Close[0];
                            double prevClose = Close[1];
                            crossedAboveTop = prevClose <= lastTop.Price && currentClose > lastTop.Price;
                            crossedBelowBottom = prevClose >= lastBottom.Price && currentClose < lastBottom.Price;

                            if (EnableDetailedLogging && (crossedAboveTop || crossedBelowBottom))
                            {
                                LogMessage("CROSS", $"Primary Cross: prevClose={prevClose}, currentClose={currentClose}, topPrice={lastTop.Price}, bottomPrice={lastBottom.Price}");
                            }
                        }

                        chochUp = crossedAboveTop && trendDir <= 0;
                        chochDown = crossedBelowBottom && trendDir >= 0;
                        bosUp = crossedAboveTop && bosUpCheck && trendDir > 0;
                        bosDown = crossedBelowBottom && bosDownCheck && trendDir < 0;

                        if (chochUp) { trendDir = 1; LogMessage("STRUCTURE", "Primary CHoCH Up -> Trend = 1"); }
                        if (chochDown) { trendDir = -1; LogMessage("STRUCTURE", "Primary CHoCH Down -> Trend = -1"); }
                        if (bosUp) { LogMessage("STRUCTURE", "Primary BOS Up -> Trend = 1"); }
                        if (bosDown) { LogMessage("STRUCTURE", "Primary BOS Down -> Trend = -1"); }
                    }
                    catch (Exception ex)
                    {
                        Print($"DTFX ERROR: Error in BOS/CHoCH detection: {ex.Message}");
                    }
                }

                // Zone Creation
                mssUp = bosUp || chochUp;
                mssDown = bosDown || chochDown;

                // Always log market structure shift status
                LogMessage("STRUCTURE", $"Market Structure Shift: Up={mssUp} (BOS={bosUp}, CHoCH={chochUp}), Down={mssDown} (BOS={bosDown}, CHoCH={chochDown})");

                if (mssUp || mssDown)
                {
                    zoneCounter++;
                    string tag = "Zone_" + zoneCounter;
                    SwingPoint sourceSwing = new SwingPoint();
                    SwingPoint targetSwing = new SwingPoint();
                    double zoneTop = 0;
                    double zoneBottom = 0;
                    bool isBullishZone = false;

                    if (mssUp)
                    {
                        LogMessage("ZONE_CREATE", $"Attempting to create bullish zone from recent swing points");

                        // Find the most recent swing low before the last swing high
                        var lastLow = swingLows.FirstOrDefault(s => s.Bar < lastTop.Bar);

                        // Log swing point details
                        LogMessage("ZONE_CREATE", $"Last Top: Bar={lastTop.Bar}, Price={lastTop.Price}, Valid={lastTop.IsValid()}");
                        LogMessage("ZONE_CREATE", $"Last Low before Top: Bar={lastLow.Bar}, Price={lastLow.Price}, Valid={lastLow.IsValid()}");

                        if (lastLow.IsValid() && lastTop.IsValid())
                        {
                            zoneTop = lastTop.Price;
                            zoneBottom = lastLow.Price;
                            isBullishZone = true;
                            sourceSwing = lastLow;
                            targetSwing = lastTop;
                            bosUpCheck = false;

                            LogMessage("ZONE_CREATE", $"Bullish zone parameters set: Top={zoneTop}, Bottom={zoneBottom}");
                        }
                        else
                        {
                            mssUp = false;
                            LogMessage("ZONE_CREATE", $"Failed to create bullish zone: Invalid swing points");
                        }
                    }
                    else if (mssDown)
                    {
                        LogMessage("ZONE_CREATE", $"Attempting to create bearish zone from recent swing points");

                        // Find the most recent swing high before the last swing low
                        var lastHigh = swingHighs.FirstOrDefault(s => s.Bar < lastBottom.Bar);

                        // Log swing point details
                        LogMessage("ZONE_CREATE", $"Last Bottom: Bar={lastBottom.Bar}, Price={lastBottom.Price}, Valid={lastBottom.IsValid()}");
                        LogMessage("ZONE_CREATE", $"Last High before Bottom: Bar={lastHigh.Bar}, Price={lastHigh.Price}, Valid={lastHigh.IsValid()}");

                        if (lastHigh.IsValid() && lastBottom.IsValid())
                        {
                            zoneTop = lastHigh.Price;
                            zoneBottom = lastBottom.Price;
                            isBullishZone = false;
                            sourceSwing = lastHigh;
                            targetSwing = lastBottom;
                            bosDownCheck = false;

                            LogMessage("ZONE_CREATE", $"Bearish zone parameters set: Top={zoneTop}, Bottom={zoneBottom}");
                        }
                        else
                        {
                            mssDown = false;
                            LogMessage("ZONE_CREATE", $"Failed to create bearish zone: Invalid swing points");
                        }
                    }

                    if ((mssUp || mssDown) && sourceSwing.IsValid() && targetSwing.IsValid() && zoneTop > zoneBottom)
                    {
                        Dictionary<double, double> fibLevels = CalculateFibLevels(zoneTop, zoneBottom, isBullishZone);
                        int zoneStartBar = Math.Max(sourceSwing.Bar, targetSwing.Bar);
                        FibZone newZone = new FibZone(zoneStartBar, zoneTop, zoneBottom, isBullishZone, sourceSwing, targetSwing, fibLevels, tag);

                        bool allowAdd = ZoneFilter == ZoneDisplayFilter.Both ||
                                       (ZoneFilter == ZoneDisplayFilter.BullishOnly && isBullishZone) ||
                                       (ZoneFilter == ZoneDisplayFilter.BearishOnly && !isBullishZone);

                        LogMessage("ZONE_CREATE", $"Zone creation result: Valid={sourceSwing.IsValid() && targetSwing.IsValid() && zoneTop > zoneBottom}, AllowAdd={allowAdd}");

                        if (allowAdd)
                        {
                            activeZones.Add(newZone);
                            latestZoneForTrading = newZone;
                            LogMessage("ZONE", $"New Primary {(isBullishZone ? "Bull" : "Bear")} Zone [{tag}] from {newZone.Bottom} to {newZone.Top}");
                            LogMessage("ZONE_TRADING", $"Set latest zone for trading: {tag}, IsBullish={isBullishZone}");
                        }
                        else
                        {
                            LogMessage("ZONE", $"Zone [{tag}] created but not added due to filter settings");
                        }
                    }
                    else
                    {
                        LogMessage("ZONE_CREATE", $"Failed to create valid zone: SourceValid={sourceSwing.IsValid()}, TargetValid={targetSwing.IsValid()}, TopBottom={zoneTop > zoneBottom}");
                    }
                }

                // Manage Active Zones
                List<FibZone> zonesToRemove = new List<FibZone>();
                activeZones.Sort((a, b) => b.StartBar.CompareTo(a.StartBar));
                int displayedCount = 0;

                foreach (FibZone zone in activeZones)
                {
                    if (!zone.IsValid) continue;

                    if (EnableZoneInvalidation)
                    {
                        double breakLevel = zone.IsBullish ? zone.Bottom : zone.Top;
                        bool invalidated = InvalidationType == InvalidationTypeOption.Close
                            ? (zone.IsBullish ? Close[0] < breakLevel : Close[0] > breakLevel)
                            : (zone.IsBullish ? Low[0] < breakLevel : High[0] > breakLevel);

                        if (invalidated)
                        {
                            zone.IsValid = false;
                            RemoveDrawingsForZone(zone.DrawTag);
                            if (latestZoneForTrading == zone) latestZoneForTrading = null;
                            if (LogZoneManagement)
                                LogMessage("ZONE", $"Zone [{zone.DrawTag}] invalidated by price {Close[0]} {(zone.IsBullish ? "below" : "above")} {(zone.IsBullish ? zone.Bottom : zone.Top)}");
                            continue;
                        }
                    }

                    zone.EndBar = CurrentBar;

                    bool shouldDisplay = DisplayAllZones;
                    if (!DisplayAllZones)
                    {
                        bool zoneMatchesFilter = ZoneFilter == ZoneDisplayFilter.Both ||
                                                (ZoneFilter == ZoneDisplayFilter.BullishOnly && zone.IsBullish) ||
                                                (ZoneFilter == ZoneDisplayFilter.BearishOnly && !zone.IsBullish);

                        if (zone.IsValid && zoneMatchesFilter)
                        {
                            if (displayedCount < ZoneDispNum)
                            {
                                shouldDisplay = true;
                                displayedCount++;
                            }
                            else
                            {
                                RemoveDrawingsForZone(zone.DrawTag);
                            }
                        }
                        else
                        {
                            RemoveDrawingsForZone(zone.DrawTag);
                        }
                    }
                    else if (!zone.IsValid)
                    {
                        RemoveDrawingsForZone(zone.DrawTag);
                    }

                    if (shouldDisplay && zone.IsValid && IsVisible)
                    {
                        DrawZoneAndFibs(zone);
                    }
                }

                int maxBarsLookBackValue = MaximumBarsLookBack == MaximumBarsLookBack.Infinite ? int.MaxValue :
                                   MaximumBarsLookBack == MaximumBarsLookBack.TwoHundredFiftySix ? 256 :
                                   MaximumBarsLookBack == MaximumBarsLookBack.Infinite ? int.MaxValue : 1024;
                activeZones.RemoveAll(z => !z.IsValid && (CurrentBar - z.StartBar > maxBarsLookBackValue * 2));

                if (latestZoneForTrading != null && (!latestZoneForTrading.IsValid || !IsZoneTypeAllowed(latestZoneForTrading)))
                {
                    latestZoneForTrading = activeZones.FirstOrDefault(z => z.IsValid && IsZoneTypeAllowed(z));
                }

                // Trailing Stop Management
                if (EnableTrailingStop && Position.MarketPosition != MarketPosition.Flat)
                {
                    if (Position.MarketPosition == MarketPosition.Long)
                    {
                        double newStop = Instrument.MasterInstrument.RoundToTickSize(High[0] - TrailingStopTicks * TickSize);
                        double currentStopPrice = 0;
                        Order workingStop = Orders.FirstOrDefault(o => o.OrderState == OrderState.Working && o.IsStopMarket && o.OrderAction == OrderAction.Sell);
                        if (workingStop != null)
                        {
                            currentStopPrice = workingStop.StopPrice;
                            if (newStop > currentStopPrice)
                            {
                                SetStopLoss(workingStop.Name, CalculationMode.Price, newStop, false);
                                LogMessage("TRADE", $"Updated trailing stop for LONG: {newStop}");
                            }
                        }
                    }
                    else if (Position.MarketPosition == MarketPosition.Short)
                    {
                        double newStop = Instrument.MasterInstrument.RoundToTickSize(Low[0] + TrailingStopTicks * TickSize);
                        double currentStopPrice = 0;
                        Order workingStop = Orders.FirstOrDefault(o => o.OrderState == OrderState.Working && o.IsStopMarket && o.OrderAction == OrderAction.Buy);
                        if (workingStop != null)
                        {
                            currentStopPrice = workingStop.StopPrice;
                            if (newStop < currentStopPrice)
                            {
                                SetStopLoss(workingStop.Name, CalculationMode.Price, newStop, false);
                                LogMessage("TRADE", $"Updated trailing stop for SHORT: {newStop}");
                            }
                        }
                    }
                }

                // Log active zones count for debugging
                LogMessage("ZONE_STATUS", $"Active zones count: {activeZones.Count(z => z.IsValid)}, Latest zone for trading: {(latestZoneForTrading != null ? latestZoneForTrading.DrawTag : "None")}");

                // Strategy Execution
                if (Position.Quantity < TradeQuantity * EntriesPerDirection)
                {
                    if (latestZoneForTrading == null)
                    {
                        LogMessage("TRADE_SCAN", "No valid zone available for trading");

                        // Try to find a valid zone if none is set
                        latestZoneForTrading = activeZones.FirstOrDefault(z => z.IsValid && IsZoneTypeAllowed(z));

                        if (latestZoneForTrading != null)
                            LogMessage("TRADE_SCAN", $"Found new zone for trading: {latestZoneForTrading.DrawTag}");
                    }

                    if (latestZoneForTrading != null)
                    {
                        LogMessage("TRADE_SCAN", $"Checking zone [{latestZoneForTrading.DrawTag}] for entry");

                        // Check if current price is inside the zone
                        bool isInZone = latestZoneForTrading.IsBullish
                            ? Low[0] <= latestZoneForTrading.Top && High[0] >= latestZoneForTrading.Bottom
                            : High[0] >= latestZoneForTrading.Bottom && Low[0] <= latestZoneForTrading.Top;

                        LogMessage("TRADE_SCAN", $"Price in zone: {isInZone}, Low={Low[0]}, High={High[0]}, Zone={latestZoneForTrading.Bottom}-{latestZoneForTrading.Top}");
                    }
                    else
                    {
                        LogMessage("TRADE_SCAN", "No valid zone available for trading after search");
                    }

                    bool entryTrigger = false;
                    bool isInZone = false;

                    if (latestZoneForTrading != null)
                    {
                        isInZone = latestZoneForTrading.IsBullish
                            ? Low[0] <= latestZoneForTrading.Top && High[0] >= latestZoneForTrading.Bottom
                            : High[0] >= latestZoneForTrading.Bottom && Low[0] <= latestZoneForTrading.Top;
                    }

                    if (isInZone && latestZoneForTrading != null)
                    {
                        bool candleConfirmation = !RequireCandleConfirmation || (latestZoneForTrading.IsBullish ? IsBullishEngulfing(1) : IsBearishEngulfing(1));

                        // Log candle confirmation status
                        if (LogTradeExecution)
                            LogMessage("TRADE_CHECK", $"Candle confirmation: {candleConfirmation}, RequireCandleConfirmation: {RequireCandleConfirmation}, IsBullish: {latestZoneForTrading.IsBullish}");

                        bool barsInZone = true;
                        if (ConfirmationBars > 1)
                        {
                            int barsCount = 0;
                            for (int i = 0; i < ConfirmationBars; i++)
                            {
                                if (currentPrimaryBar - i < 0) break;
                                bool barInZone = latestZoneForTrading.IsBullish
                                    ? Low[i] <= latestZoneForTrading.Top && High[i] >= latestZoneForTrading.Bottom
                                    : High[i] >= latestZoneForTrading.Bottom && Low[i] <= latestZoneForTrading.Top;

                                if (barInZone)
                                    barsCount++;

                                // Log each bar's zone status
                                if (LogTradeExecution)
                                    LogMessage("TRADE_CHECK", $"Bar[{i}] in zone: {barInZone}, Low: {Low[i]}, High: {High[i]}, Zone: {latestZoneForTrading.Bottom}-{latestZoneForTrading.Top}");
                            }
                            barsInZone = barsCount >= ConfirmationBars;

                            // Log confirmation bars status
                            if (LogTradeExecution)
                                LogMessage("TRADE_CHECK", $"Bars in zone: {barsInZone}, Required: {ConfirmationBars}, Found: {barsCount}");
                        }

                        if (latestZoneForTrading.IsBullish && Close[0] > latestZoneForTrading.Bottom && candleConfirmation && barsInZone)
                        {
                            entryTrigger = true;
                            if (LogTradeExecution)
                                LogMessage("TRADE_TRIGGER", $"Bullish entry trigger: Close={Close[0]} > Bottom={latestZoneForTrading.Bottom}");
                        }
                        else if (!latestZoneForTrading.IsBullish && Close[0] < latestZoneForTrading.Top && candleConfirmation && barsInZone)
                        {
                            entryTrigger = true;
                            if (LogTradeExecution)
                                LogMessage("TRADE_TRIGGER", $"Bearish entry trigger: Close={Close[0]} < Top={latestZoneForTrading.Top}");
                        }
                        else if (LogTradeExecution)
                        {
                            // Log why entry trigger was not set
                            if (latestZoneForTrading.IsBullish)
                                LogMessage("TRADE_CHECK", $"Bullish entry conditions not met: Close > Bottom: {Close[0] > latestZoneForTrading.Bottom}, Candle Confirmation: {candleConfirmation}, Bars in Zone: {barsInZone}");
                            else
                                LogMessage("TRADE_CHECK", $"Bearish entry conditions not met: Close < Top: {Close[0] < latestZoneForTrading.Top}, Candle Confirmation: {candleConfirmation}, Bars in Zone: {barsInZone}");
                        }
                    }

                    if (entryTrigger)
                    {
                        // Always log entry trigger status
                        LogMessage("TRADE_STATUS", $"Entry trigger activated for {(latestZoneForTrading.IsBullish ? "BULLISH" : "BEARISH")} zone");

                        bool htfAlignmentCheck = !EnableHTFFilter;
                        if (EnableHTFFilter)
                        {
                            htfAlignmentCheck = latestZoneForTrading.IsBullish ? htfTrendDir >= 0 : htfTrendDir <= 0;
                            // Always log HTF filter status
                            LogMessage("TRADE_FILTER", $"HTF Filter: Zone={(latestZoneForTrading.IsBullish ? "Bull" : "Bear")}, HTF Trend={htfTrendDir}, Alignment={htfAlignmentCheck}");
                        }
                        else
                        {
                            LogMessage("TRADE_FILTER", "HTF Filter disabled, alignment check passed by default");
                        }

                        bool htfZoneConfluence = !EnableHTFZones;
                        if (EnableHTFZones)
                        {
                            // Log HTF zones for debugging
                            LogMessage("TRADE_CONFLUENCE", $"Checking HTF zones, count: {htfActiveZones.Count(z => z.IsValid)}");

                            foreach (var htfZone in htfActiveZones.Where(z => z.IsValid))
                            {
                                bool directionMatch = htfZone.IsBullish == latestZoneForTrading.IsBullish;
                                bool topMatch = Math.Abs(htfZone.Top - latestZoneForTrading.Top) < TickSize * 10;
                                bool bottomMatch = Math.Abs(htfZone.Bottom - latestZoneForTrading.Bottom) < TickSize * 10;

                                LogMessage("TRADE_CONFLUENCE", $"HTF Zone {htfZone.DrawTag}: Direction Match={directionMatch}, Top Match={topMatch}, Bottom Match={bottomMatch}");
                            }

                            htfZoneConfluence = htfActiveZones.Any(z => z.IsValid && z.IsBullish == latestZoneForTrading.IsBullish &&
                                                                       Math.Abs(z.Top - latestZoneForTrading.Top) < TickSize * 10 &&
                                                                       Math.Abs(z.Bottom - latestZoneForTrading.Bottom) < TickSize * 10);

                            LogMessage("TRADE_CONFLUENCE", $"HTF Zone Confluence: {htfZoneConfluence}");
                        }
                        else
                        {
                            LogMessage("TRADE_CONFLUENCE", "HTF Zone Confluence disabled, check passed by default");
                        }

                        if (htfAlignmentCheck && htfZoneConfluence)
                        {
                            double stopLossPrice = 0;
                            double takeProfitPrice = 0;

                            // Log that we've passed all filters and are checking for valid trade setup
                            LogMessage("TRADE_STATUS", "Passed all filters, checking for valid trade setup");

                            if (latestZoneForTrading.IsBullish)
                            {
                                // Log source and target swing validity
                                LogMessage("TRADE_SETUP", $"LONG setup check - Source swing valid: {latestZoneForTrading.SourceSwing.IsValid()}, Target swing valid: {latestZoneForTrading.TargetSwing.IsValid()}");

                                if (latestZoneForTrading.SourceSwing.IsValid() && latestZoneForTrading.TargetSwing.IsValid())
                                {
                                    // Calculate stop loss with fixed ticks below source swing
                                    stopLossPrice = Instrument.MasterInstrument.RoundToTickSize(latestZoneForTrading.SourceSwing.Price - StopLossTicks * TickSize);
                                    double riskPoints = Close[0] - stopLossPrice;

                                    // Calculate take profit based on risk-reward ratio
                                    double minTakeProfit = Close[0] + (riskPoints * MinRiskRewardRatio);
                                    takeProfitPrice = Instrument.MasterInstrument.RoundToTickSize(Math.Max(latestZoneForTrading.TargetSwing.Price, minTakeProfit));

                                    // Calculate actual risk-reward ratio
                                    double actualRR = (takeProfitPrice - Close[0]) / (Close[0] - stopLossPrice);

                                    // Always log trade calculations
                                    LogMessage("TRADE_CALC", $"LONG setup: Entry={Close[0]}, SL={stopLossPrice}, TP={takeProfitPrice}, R:R={actualRR:F2}, Min R:R={MinRiskRewardRatio:F2}");

                                    // Check if entry price is above stop loss and take profit is above entry
                                    bool validStopLoss = Close[0] > stopLossPrice;
                                    bool validTakeProfit = takeProfitPrice > Close[0];
                                    bool validRiskReward = actualRR >= MinRiskRewardRatio;

                                    LogMessage("TRADE_VALIDATION", $"LONG validation: Valid SL={validStopLoss}, Valid TP={validTakeProfit}, Valid R:R={validRiskReward}");

                                    if (validStopLoss && validTakeProfit)
                                    {
                                        LogMessage("TRADE_ENTRY", $"ENTER LONG: Zone={latestZoneForTrading.DrawTag} Entry={Close[0]} SL={stopLossPrice} TP={takeProfitPrice} HTF={htfTrendDir}");
                                        EnterLong(TradeQuantity, "Long_" + latestZoneForTrading.DrawTag);
                                        SetStopLoss("Long_" + latestZoneForTrading.DrawTag, CalculationMode.Price, stopLossPrice, false);
                                        SetProfitTarget("Long_" + latestZoneForTrading.DrawTag, CalculationMode.Price, takeProfitPrice, false);
                                        latestZoneForTrading = null;
                                    }
                                    else
                                    {
                                        LogMessage("TRADE_REJECT", $"LONG rejected: Invalid setup - Valid SL={validStopLoss}, Valid TP={validTakeProfit}, Valid R:R={validRiskReward}");
                                    }
                                }
                                else
                                {
                                    LogMessage("TRADE_REJECT", $"LONG rejected: Invalid swing points - Source: {latestZoneForTrading.SourceSwing.Price}, Target: {latestZoneForTrading.TargetSwing.Price}");
                                }
                            }
                            else // Bearish zone
                            {
                                // Log source and target swing validity
                                LogMessage("TRADE_SETUP", $"SHORT setup check - Source swing valid: {latestZoneForTrading.SourceSwing.IsValid()}, Target swing valid: {latestZoneForTrading.TargetSwing.IsValid()}");

                                if (latestZoneForTrading.SourceSwing.IsValid() && latestZoneForTrading.TargetSwing.IsValid())
                                {
                                    // Calculate stop loss with fixed ticks above source swing
                                    stopLossPrice = Instrument.MasterInstrument.RoundToTickSize(latestZoneForTrading.SourceSwing.Price + StopLossTicks * TickSize);
                                    double riskPoints = stopLossPrice - Close[0];

                                    // Calculate take profit based on risk-reward ratio
                                    double minTakeProfit = Close[0] - (riskPoints * MinRiskRewardRatio);
                                    takeProfitPrice = Instrument.MasterInstrument.RoundToTickSize(Math.Min(latestZoneForTrading.TargetSwing.Price, minTakeProfit));

                                    // Calculate actual risk-reward ratio
                                    double actualRR = (Close[0] - takeProfitPrice) / (stopLossPrice - Close[0]);

                                    // Always log trade calculations
                                    LogMessage("TRADE_CALC", $"SHORT setup: Entry={Close[0]}, SL={stopLossPrice}, TP={takeProfitPrice}, R:R={actualRR:F2}, Min R:R={MinRiskRewardRatio:F2}");

                                    // Check if entry price is below stop loss and take profit is below entry
                                    bool validStopLoss = Close[0] < stopLossPrice;
                                    bool validTakeProfit = takeProfitPrice < Close[0];
                                    bool validRiskReward = actualRR >= MinRiskRewardRatio;

                                    LogMessage("TRADE_VALIDATION", $"SHORT validation: Valid SL={validStopLoss}, Valid TP={validTakeProfit}, Valid R:R={validRiskReward}");

                                    if (validStopLoss && validTakeProfit)
                                    {
                                        LogMessage("TRADE_ENTRY", $"ENTER SHORT: Zone={latestZoneForTrading.DrawTag} Entry={Close[0]} SL={stopLossPrice} TP={takeProfitPrice} HTF={htfTrendDir}");
                                        EnterShort(TradeQuantity, "Short_" + latestZoneForTrading.DrawTag);
                                        SetStopLoss("Short_" + latestZoneForTrading.DrawTag, CalculationMode.Price, stopLossPrice, false);
                                        SetProfitTarget("Short_" + latestZoneForTrading.DrawTag, CalculationMode.Price, takeProfitPrice, false);
                                        latestZoneForTrading = null;
                                    }
                                    else
                                    {
                                        LogMessage("TRADE_REJECT", $"SHORT rejected: Invalid setup - Valid SL={validStopLoss}, Valid TP={validTakeProfit}, Valid R:R={validRiskReward}");
                                    }
                                }
                                else
                                {
                                    LogMessage("TRADE_REJECT", $"SHORT rejected: Invalid swing points - Source: {latestZoneForTrading.SourceSwing.Price}, Target: {latestZoneForTrading.TargetSwing.Price}");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"DTFX ERROR: Error in OnBarUpdate: {ex.Message}");
            }
        }
        #endregion

        #region Helper Methods
        private void CalculateHTFStructure()
        {
            try
            {
                if (BarsArray == null || CurrentBars == null || HTFBarsIndex >= CurrentBars.Length)
                {
                    Print("DTFX ERROR: Critical objects are null in CalculateHTFStructure");
                    return;
                }

                int requiredBars = StructureLength * 2 + 5;
                if (CurrentBars[HTFBarsIndex] < requiredBars)
                {
                    if (EnableDetailedLogging) Print($"DTFX: HTF Structure - Not enough bars: {CurrentBars[HTFBarsIndex]} < {requiredBars}");
                    return;
                }

                if (CurrentBar == 219)
                {
                    Print($"DTFX: HTF Structure on bar 219: CurrentBars[HTFBarsIndex]={CurrentBars[HTFBarsIndex]}, StructureLength={StructureLength}");
                }

                int currentHTFBar = CurrentBars[HTFBarsIndex];
                double highestHighHTF = double.MinValue;
                double lowestLowHTF = double.MaxValue;

                for (int i = 1; i <= StructureLength; i++)
                {
                    if (currentHTFBar - i < 0) break;
                    highestHighHTF = Math.Max(highestHighHTF, BarsArray[HTFBarsIndex].GetHigh(i));
                    lowestLowHTF = Math.Min(lowestLowHTF, BarsArray[HTFBarsIndex].GetLow(i));
                }

                if (highestHighHTF == double.MinValue || lowestLowHTF == double.MaxValue)
                {
                    if (EnableDetailedLogging) Print("DTFX: HTF Structure - Could not calculate valid high/low values");
                    return;
                }

                // HTF Swing High
                bool canCheckSwingHigh = htfDir >= 0 && currentHTFBar >= StructureLength + 2;
                bool isSwingHighCandidate = false;
                if (canCheckSwingHigh)
                {
                    try
                    {
                        isSwingHighCandidate = BarsArray[HTFBarsIndex].GetHigh(StructureLength) >= highestHighHTF;
                    }
                    catch (Exception ex)
                    {
                        Print($"DTFX ERROR: Error checking HTF swing high: {ex.Message}");
                        isSwingHighCandidate = false;
                    }
                }

                if (canCheckSwingHigh && isSwingHighCandidate)
                {
                    int potentialTopBarHTF = -1;
                    double potentialTopPriceHTF = double.MinValue;
                    for (int i = 1; i <= StructureLength; i++)
                    {
                        if (currentHTFBar - i < 0) break;
                        try
                        {
                            if (BarsArray[HTFBarsIndex].GetHigh(i) > potentialTopPriceHTF)
                            {
                                potentialTopPriceHTF = BarsArray[HTFBarsIndex].GetHigh(i);
                                potentialTopBarHTF = currentHTFBar - i;
                            }
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error in HTF swing high loop at i={i}: {ex.Message}");
                            break;
                        }
                    }

                    bool isValidSwingHigh = potentialTopBarHTF != -1 && currentHTFBar >= StructureLength + 2;
                    if (isValidSwingHigh)
                    {
                        try
                        {
                            isValidSwingHigh = potentialTopPriceHTF > BarsArray[HTFBarsIndex].GetHigh(StructureLength + 1);
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error comparing HTF previous bar: {ex.Message}");
                            isValidSwingHigh = false;
                        }
                    }

                    if (isValidSwingHigh && (potentialTopBarHTF > htfLastTop.Bar || !htfLastTop.IsValid()))
                    {
                        if (potentialTopPriceHTF > htfLastTop.Price || !htfLastTop.IsValid())
                        {
                            htfDir = -1;
                            htfLastTop = new SwingPoint(potentialTopPriceHTF, potentialTopBarHTF);
                            htfSwingHighs.Add(htfLastTop);
                            htfSwingHighs.Sort((a, b) => b.Bar.CompareTo(a.Bar));
                            if (htfSwingHighs.Count > 10) htfSwingHighs.RemoveAt(htfSwingHighs.Count - 1);
                            htfBosUpCheck = true;
                            LogMessage("STRUCTURE", $"New HTF High: {htfLastTop.Price} at Bar {htfLastTop.Bar}");
                        }
                    }
                }

                // HTF Swing Low
                bool canCheckSwingLow = htfDir <= 0 && currentHTFBar >= StructureLength + 2;
                bool isSwingLowCandidate = false;
                if (canCheckSwingLow)
                {
                    try
                    {
                        isSwingLowCandidate = BarsArray[HTFBarsIndex].GetLow(StructureLength) <= lowestLowHTF;
                    }
                    catch (Exception ex)
                    {
                        Print($"DTFX ERROR: Error checking HTF swing low: {ex.Message}");
                        isSwingLowCandidate = false;
                    }
                }

                if (canCheckSwingLow && isSwingLowCandidate)
                {
                    int potentialBotBarHTF = -1;
                    double potentialBotPriceHTF = double.MaxValue;
                    for (int i = 1; i <= StructureLength; i++)
                    {
                        if (currentHTFBar - i < 0) break;
                        try
                        {
                            if (BarsArray[HTFBarsIndex].GetLow(i) < potentialBotPriceHTF)
                            {
                                potentialBotPriceHTF = BarsArray[HTFBarsIndex].GetLow(i);
                                potentialBotBarHTF = currentHTFBar - i;
                            }
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error in HTF swing low loop at i={i}: {ex.Message}");
                            break;
                        }
                    }

                    bool isValidSwingLow = potentialBotBarHTF != -1 && currentHTFBar >= StructureLength + 2;
                    if (isValidSwingLow)
                    {
                        try
                        {
                            isValidSwingLow = potentialBotPriceHTF < BarsArray[HTFBarsIndex].GetLow(StructureLength + 1);
                        }
                        catch (Exception ex)
                        {
                            Print($"DTFX ERROR: Error comparing HTF previous bar: {ex.Message}");
                            isValidSwingLow = false;
                        }
                    }

                    if (isValidSwingLow && (potentialBotBarHTF > htfLastBottom.Bar || !htfLastBottom.IsValid()))
                    {
                        if (potentialBotPriceHTF < htfLastBottom.Price || !htfLastBottom.IsValid())
                        {
                            htfDir = 1;
                            htfLastBottom = new SwingPoint(potentialBotPriceHTF, potentialBotBarHTF);
                            htfSwingLows.Add(htfLastBottom);
                            htfSwingLows.Sort((a, b) => b.Bar.CompareTo(a.Bar));
                            if (htfSwingLows.Count > 10) htfSwingLows.RemoveAt(htfSwingLows.Count - 1);
                            htfBosDownCheck = true;
                            LogMessage("STRUCTURE", $"New HTF Low: {htfLastBottom.Price} at Bar {htfLastBottom.Bar}");
                        }
                    }
                }

                // HTF BOS/CHoCH Detection
                bool htfMssUp = false;
                bool htfMssDown = false;
                if (htfLastTop.IsValid() && htfLastBottom.IsValid() && currentHTFBar >= 3)
                {
                    try
                    {
                        bool crossedAboveTop = false;
                        bool crossedBelowBottom = false;
                        if (currentHTFBar >= 2)
                        {
                            double currentClose = BarsArray[HTFBarsIndex].GetClose(0);
                            double prevClose = BarsArray[HTFBarsIndex].GetClose(1);
                            crossedAboveTop = prevClose <= htfLastTop.Price && currentClose > htfLastTop.Price;
                            crossedBelowBottom = prevClose >= htfLastBottom.Price && currentClose < htfLastBottom.Price;

                            if (EnableDetailedLogging && (crossedAboveTop || crossedBelowBottom))
                            {
                                LogMessage("HTF_CROSS", $"HTF Cross: prevClose={prevClose}, currentClose={currentClose}, topPrice={htfLastTop.Price}, bottomPrice={htfLastBottom.Price}");
                            }
                        }

                        bool chochUpHTF = crossedAboveTop && htfTrendDir <= 0;
                        bool chochDownHTF = crossedBelowBottom && htfTrendDir >= 0;
                        bool bosUpHTF = crossedAboveTop && htfBosUpCheck && htfTrendDir > 0;
                        bool bosDownHTF = crossedBelowBottom && htfBosDownCheck && htfTrendDir < 0;

                        if (chochUpHTF) { htfTrendDir = 1; LogMessage("STRUCTURE", $"HTF CHoCH Up -> Trend = {htfTrendDir}"); }
                        if (chochDownHTF) { htfTrendDir = -1; LogMessage("STRUCTURE", $"HTF CHoCH Down -> Trend = {htfTrendDir}"); }
                        if (bosUpHTF) { htfBosUpCheck = false; LogMessage("STRUCTURE", $"HTF BOS Up -> Trend = {htfTrendDir}"); }
                        if (bosDownHTF) { htfBosDownCheck = false; LogMessage("STRUCTURE", $"HTF BOS Down -> Trend = {htfTrendDir}"); }

                        htfMssUp = chochUpHTF || bosUpHTF;
                        htfMssDown = chochDownHTF || bosDownHTF;

                        // HTF Zone Creation
                        if (EnableHTFZones && (htfMssUp || htfMssDown))
                        {
                            htfZoneCounter++;
                            string tag = "HTF_Zone_" + htfZoneCounter;
                            SwingPoint sourceSwing = new SwingPoint();
                            SwingPoint targetSwing = new SwingPoint();
                            double zoneTop = 0;
                            double zoneBottom = 0;
                            bool isBullishZone = false;

                            if (htfMssUp)
                            {
                                var lastLow = htfSwingLows.FirstOrDefault(s => s.Bar < htfLastTop.Bar);
                                if (lastLow.IsValid() && htfLastTop.IsValid())
                                {
                                    zoneTop = htfLastTop.Price;
                                    zoneBottom = lastLow.Price;
                                    isBullishZone = true;
                                    sourceSwing = lastLow;
                                    targetSwing = htfLastTop;
                                    htfBosUpCheck = false;
                                }
                                else { htfMssUp = false; }
                            }
                            else if (htfMssDown)
                            {
                                var lastHigh = htfSwingHighs.FirstOrDefault(s => s.Bar < htfLastBottom.Bar);
                                if (lastHigh.IsValid() && htfLastBottom.IsValid())
                                {
                                    zoneTop = lastHigh.Price;
                                    zoneBottom = htfLastBottom.Price;
                                    isBullishZone = false;
                                    sourceSwing = lastHigh;
                                    targetSwing = htfLastBottom;
                                    htfBosDownCheck = false;
                                }
                                else { htfMssDown = false; }
                            }

                            if ((htfMssUp || htfMssDown) && sourceSwing.IsValid() && targetSwing.IsValid() && zoneTop > zoneBottom)
                            {
                                Dictionary<double, double> fibLevels = CalculateFibLevels(zoneTop, zoneBottom, isBullishZone);
                                int zoneStartBar = Math.Max(sourceSwing.Bar, targetSwing.Bar);
                                FibZone newZone = new FibZone(zoneStartBar, zoneTop, zoneBottom, isBullishZone, sourceSwing, targetSwing, fibLevels, tag);
                                htfActiveZones.Add(newZone);
                                if (LogZoneManagement)
                                    LogMessage("ZONE", $"New HTF {(isBullishZone ? "Bull" : "Bear")} Zone [{tag}] from {newZone.Bottom} to {newZone.Top}");
                            }
                        }

                        // Manage HTF Zones
                        if (EnableHTFZones)
                        {
                            htfActiveZones.Sort((a, b) => b.StartBar.CompareTo(a.StartBar));
                            foreach (FibZone zone in htfActiveZones.ToList())
                            {
                                if (!zone.IsValid) continue;

                                if (EnableZoneInvalidation)
                                {
                                    double breakLevel = zone.IsBullish ? zone.Bottom : zone.Top;
                                    bool invalidated = InvalidationType == InvalidationTypeOption.Close
                                        ? (zone.IsBullish ? BarsArray[HTFBarsIndex].GetClose(0) < breakLevel : BarsArray[HTFBarsIndex].GetClose(0) > breakLevel)
                                        : (zone.IsBullish ? BarsArray[HTFBarsIndex].GetLow(0) < breakLevel : BarsArray[HTFBarsIndex].GetHigh(0) > breakLevel);

                                    if (invalidated)
                                    {
                                        zone.IsValid = false;
                                        if (LogZoneManagement)
                                            LogMessage("ZONE", $"HTF Zone [{zone.DrawTag}] invalidated");
                                        continue;
                                    }
                                }

                                zone.EndBar = currentHTFBar;
                            }

                            int htfMaxBarsLookBackValue = MaximumBarsLookBack == MaximumBarsLookBack.Infinite ? int.MaxValue :
                                                MaximumBarsLookBack == MaximumBarsLookBack.TwoHundredFiftySix ? 256 :
                                                MaximumBarsLookBack == MaximumBarsLookBack.Infinite ? int.MaxValue : 1024;
                            htfActiveZones.RemoveAll(z => !z.IsValid && (currentHTFBar - z.StartBar > htfMaxBarsLookBackValue * 2));
                        }
                    }
                    catch (Exception ex)
                    {
                        Print($"DTFX ERROR: Error in HTF BOS/CHoCH detection: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"DTFX ERROR: Error in CalculateHTFStructure: {ex.Message}");
            }
        }

        private Dictionary<double, double> CalculateFibLevels(double top, double bottom, bool isBullish)
        {
            var levels = new Dictionary<double, double>();
            double range = top - bottom;
            if (range <= 0 || !FiniteDouble(top) || !FiniteDouble(bottom)) return levels;

            double calculationAnchor = isBullish ? bottom : top;
            double calculationTarget = isBullish ? top : bottom;
            range = Math.Abs(calculationTarget - calculationAnchor);

            if (F1Tog && FiniteDouble(F1Lvl) && F1Lvl >= 0 && F1Lvl <= 2)
                levels.Add(F1Lvl, Instrument.MasterInstrument.RoundToTickSize(calculationAnchor + range * F1Lvl * (isBullish ? 1 : -1)));
            if (F2Tog && FiniteDouble(F2Lvl) && F2Lvl >= 0 && F2Lvl <= 2)
                levels.Add(F2Lvl, Instrument.MasterInstrument.RoundToTickSize(calculationAnchor + range * F2Lvl * (isBullish ? 1 : -1)));
            if (F3Tog && FiniteDouble(F3Lvl) && F3Lvl >= 0 && F3Lvl <= 2)
                levels.Add(F3Lvl, Instrument.MasterInstrument.RoundToTickSize(calculationAnchor + range * F3Lvl * (isBullish ? 1 : -1)));
            if (F4Tog && FiniteDouble(F4Lvl) && F4Lvl >= 0 && F4Lvl <= 2)
                levels.Add(F4Lvl, Instrument.MasterInstrument.RoundToTickSize(calculationAnchor + range * F4Lvl * (isBullish ? 1 : -1)));
            if (F5Tog && FiniteDouble(F5Lvl) && F5Lvl >= 0 && F5Lvl <= 2)
                levels.Add(F5Lvl, Instrument.MasterInstrument.RoundToTickSize(calculationAnchor + range * F5Lvl * (isBullish ? 1 : -1)));

            return levels;
        }

        private void DrawZoneAndFibs(FibZone zone)
        {
            try
            {
                if (zone == null || !zone.IsValid || Instrument == null || BarsArray == null || CurrentBar < 1)
                {
                    Print("DTFX: Cannot draw zone - null reference or invalid state");
                    return;
                }

                if (zone.FibLevels == null)
                {
                    Print("DTFX ERROR: Zone FibLevels is null");
                    return;
                }

                int startBarOffset = CurrentBar - zone.StartBar;
                int endBarOffset = CurrentBar - zone.EndBar;
                if (startBarOffset < 0 || startBarOffset >= CurrentBar || endBarOffset >= CurrentBar) return;

                startBarOffset = Math.Min(startBarOffset, CurrentBar - 1);
                endBarOffset = Math.Min(endBarOffset, CurrentBar - 1);

                if (startBarOffset < 0 || endBarOffset < 0)
                {
                    if (EnableDetailedLogging)
                        LogMessage("DRAW_ERROR", $"Invalid bar offsets: start={startBarOffset}, end={endBarOffset}");
                    return;
                }

                Brush zoneBrush = zone.IsBullish ? BullZoneBrush : BearZoneBrush;
                string boxTag = zone.DrawTag + "_Box";

                try
                {
                    SafeRemoveDrawObject(boxTag);
                    Draw.Rectangle(this, boxTag, false, startBarOffset, zone.Top, ExtendBoxes ? -LookForwardBars : endBarOffset, zone.Bottom, Brushes.Transparent, zoneBrush, 1, true);
                }
                catch (ArgumentException ex)
                {
                    LogMessage("DRAW_ERROR", $"Invalid drawing parameters for rectangle {boxTag}: {ex.Message}");
                }
                catch (Exception ex)
                {
                    LogMessage("DRAW_ERROR", $"Unexpected error drawing rectangle {boxTag}: {ex.Message}");
                }

                Action<double, bool, Brush, Brush, LineStyleOption> drawFibLine = (levelKey, isEnabled, bullBrush, bearBrush, style) =>
                {
                    try
                    {
                        if (isEnabled && zone.FibLevels.ContainsKey(levelKey) && FiniteDouble(zone.FibLevels[levelKey]))
                        {
                            double fibPrice = zone.FibLevels[levelKey];
                            Brush lineBrush = zone.IsBullish ? bullBrush : bearBrush;
                            DashStyleHelper dashStyle = GetDashStyle(style);
                            string lineTag = zone.DrawTag + "_Fib_" + levelKey.ToString("F3");

                            try
                            {
                                SafeRemoveDrawObject(lineTag);
                                int endBar = ExtendZones ? -LookForwardBars : endBarOffset;
                                Draw.Line(this, lineTag, false, startBarOffset, fibPrice, endBar, fibPrice, lineBrush, dashStyle, 1, true);
                            }
                            catch (ArgumentException ex)
                            {
                                LogMessage("DRAW_ERROR", $"Invalid drawing parameters for fib line {lineTag}: {ex.Message}");
                            }
                            catch (Exception ex)
                            {
                                LogMessage("DRAW_ERROR", $"Unexpected error drawing fib line {lineTag}: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage("DRAW_ERROR", $"Error in drawFibLine for level {levelKey}: {ex.Message}");
                    }
                };

                drawFibLine(F1Lvl, F1Tog, F1BullBrush, F1BearBrush, F1Style);
                drawFibLine(F2Lvl, F2Tog, F2BullBrush, F2BearBrush, F2Style);
                drawFibLine(F3Lvl, F3Tog, F3BullBrush, F3BearBrush, F3Style);
                drawFibLine(F4Lvl, F4Tog, F4BullBrush, F4BearBrush, F4Style);
                drawFibLine(F5Lvl, F5Tog, F5BullBrush, F5BearBrush, F5Style);
            }
            catch (Exception ex)
            {
            }
        }

        // This method is a wrapper around the built-in NinjaTrader RemoveDrawObject method
        private void SafeRemoveDrawObject(string tag)
        {
            try
            {
                // Use NinjaTrader's built-in method to remove drawing objects
                RemoveDrawObject(tag);
            }
            catch (Exception ex)
            {
                LogMessage("DRAW_ERROR", $"Error in RemoveDrawObject for tag {tag}: {ex.Message}");
            }
        }

        private void RemoveDrawingsForZone(string zoneTagPrefix)
        {
            try
            {
                if (string.IsNullOrEmpty(zoneTagPrefix)) return;

                try
                {
                    SafeRemoveDrawObject(zoneTagPrefix + "_Box");
                }
                catch (Exception ex)
                {
                    LogMessage("DRAW_ERROR", $"Error removing box drawing: {ex.Message}");
                }

                Action<double, bool> removeFibLine = (levelKey, isEnabled) =>
                {
                    try
                    {
                        if (isEnabled)
                            SafeRemoveDrawObject(zoneTagPrefix + "_Fib_" + levelKey.ToString("F3"));
                    }
                    catch (Exception ex)
                    {
                        LogMessage("DRAW_ERROR", $"Error removing fib line {levelKey}: {ex.Message}");
                    }
                };

                removeFibLine(F1Lvl, F1Tog);
                removeFibLine(F2Lvl, F2Tog);
                removeFibLine(F3Lvl, F3Tog);
                removeFibLine(F4Lvl, F4Tog);
                removeFibLine(F5Lvl, F5Tog);
            }
            catch (Exception ex)
            {
                LogMessage("DRAW_ERROR", $"Error in RemoveDrawingsForZone: {ex.Message}");
            }
        }

        private bool IsZoneTypeAllowed(FibZone zone)
        {
            if (zone == null) return false;
            return ZoneFilter == ZoneDisplayFilter.Both ||
                   (ZoneFilter == ZoneDisplayFilter.BullishOnly && zone.IsBullish) ||
                   (ZoneFilter == ZoneDisplayFilter.BearishOnly && !zone.IsBullish);
        }

        private DashStyleHelper GetDashStyle(LineStyleOption style)
        {
            switch (style)
            {
                case LineStyleOption.Solid: return DashStyleHelper.Solid;
                case LineStyleOption.Dash: return DashStyleHelper.Dash;
                case LineStyleOption.Dot: return DashStyleHelper.Dot;
                default: return DashStyleHelper.Solid;
            }
        }

        private Brush AdjustOpacity(Brush originalBrush, int opacityPercent)
        {
            if (originalBrush == null || !(originalBrush is SolidColorBrush)) return originalBrush;
            SolidColorBrush brush = (SolidColorBrush)originalBrush.Clone();
            byte alpha = (byte)Math.Max(0, Math.Min(255, opacityPercent / 100.0 * 255));
            brush.Color = Color.FromArgb(alpha, brush.Color.R, brush.Color.G, brush.Color.B);
            brush.Freeze();
            return brush;
        }

        private bool FiniteDouble(double value)
        {
            return !double.IsNaN(value) && !double.IsInfinity(value);
        }

        private bool IsBullishEngulfing(int barsAgo)
        {
            if (CurrentBar < barsAgo + 1) return false;
            return Close[barsAgo] > Open[barsAgo] &&
                   Close[barsAgo] > Open[barsAgo + 1] &&
                   Open[barsAgo] < Close[barsAgo + 1];
        }

        private bool IsBearishEngulfing(int barsAgo)
        {
            if (CurrentBar < barsAgo + 1) return false;
            return Close[barsAgo] < Open[barsAgo] &&
                   Close[barsAgo] < Open[barsAgo + 1] &&
                   Open[barsAgo] > Close[barsAgo + 1];
        }

        private void LogMessage(string category, string message)
        {
            try
            {
                if (Bars == null || Bars.Count == 0 || Time == null || Time.Count == 0)
                {
                    Print($"DTFX: [{category}]: {message}");
                    return;
                }

                string timestamp = Time[0].ToString("yyyy-MM-dd HH:mm:ss");
                Print($"DTFX [{timestamp}] [{category}]: {message}");

                string logDirectory = Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "log");
                if (!Directory.Exists(logDirectory))
                    Directory.CreateDirectory(logDirectory);

                string logFile = Path.Combine(logDirectory, $"DTFX_Log_{Instrument.FullName}_{DateTime.Now:yyyyMMdd}.txt");
                using (StreamWriter writer = new StreamWriter(logFile, true))
                {
                    writer.WriteLine($"[{timestamp}] [{category}]: {message}");
                }
            }
            catch (Exception ex)
            {
                Print($"DTFX ERROR: Logging error: {ex.Message}. Original message: {category} - {message}");
            }
        }
        #endregion
    }
}