using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;

namespace NinjaTrader.NinjaScript.Strategies
{
    public class SampleAtmCandleStrategy : Strategy
    {
        private string atmStrategyId = string.Empty;
        private string orderId = string.Empty;
        private bool isAtmStrategyCreated = false;
        private int tradeSize = 1; // Default trade size

        // TypeConverter for ATM strategy templates
        public class AtmStrategyConverter : TypeConverter
        {
            public override bool GetStandardValuesSupported(ITypeDescriptorContext context) { return true; }
            public override bool GetStandardValuesExclusive(ITypeDescriptorContext context) { return true; }

            public override StandardValuesCollection GetStandardValues(ITypeDescriptorContext context)
            {
                string atmFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "NinjaTrader 8", "templates", "AtmStrategy");
                List<string> atmStrategies = new List<string> { "None" };

                if (Directory.Exists(atmFolder))
                {
                    foreach (string file in Directory.GetFiles(atmFolder, "*.xml"))
                    {
                        atmStrategies.Add(Path.GetFileNameWithoutExtension(file));
                    }
                }

                return new StandardValuesCollection(atmStrategies);
            }
        }

        // TypeConverter for Order Types
        public class OrderTypeConverter : TypeConverter
        {
            public override bool GetStandardValuesSupported(ITypeDescriptorContext context) { return true; }
            public override bool GetStandardValuesExclusive(ITypeDescriptorContext context) { return true; }

            public override StandardValuesCollection GetStandardValues(ITypeDescriptorContext context)
            {
                return new StandardValuesCollection(new List<string> { "Market", "Limit", "Stop", "Stop Limit" });
            }
        }

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Candlestick strategy with ATM order management, dropdown for ATM template, and order type selection";
                Name = "SampleAtmCandleStrategy";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = false;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 2;
                IsInstantiatedOnEachOptimizationIteration = false;

                // Initialize parameter defaults
                TradeSizeParameter = 1; // Default 1 contract/lot
                AtmStrategyTemplate = "None"; // Default ATM template
                OrderTypeSelection = "Limit"; // Default order type
            }
            else if (State == State.DataLoaded)
            {
                // Initialize parameters if they weren't set properly
                if (TradeSizeParameter <= 0) TradeSizeParameter = tradeSize;
                if (string.IsNullOrEmpty(AtmStrategyTemplate)) AtmStrategyTemplate = "None";
                if (string.IsNullOrEmpty(OrderTypeSelection)) OrderTypeSelection = "Limit";
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < BarsRequiredToTrade)
                return;

            // Prevent execution against historical data
            if (State == State.Historical)
                return;

            // Submits an entry order if both order id and strategy id are in a reset state
            // **** YOU MUST HAVE AT LEAST ONE ATM STRATEGY TEMPLATE IN NINJATRADER FOR THIS TO WORK ****
            if (orderId.Length == 0 && atmStrategyId.Length == 0 && AtmStrategyTemplate != "None")
            {
                bool isBearishSignal = false;
                bool isBullishSignal = false;

                // --- Signal 2 (Python) -> Leads to a BUY (Fade) ---
                bool c0_bear = Open[0] > Close[0];
                bool c1_bear = High[0] > High[1];
                bool c2_bear = Low[0] < Low[1];
                bool c3_bear = Close[0] < Low[1];

                if (c0_bear && c1_bear && c2_bear && c3_bear)
                {
                    isBearishSignal = true;
                }

                // --- Signal 1 (Python) -> Leads to a SELL (Fade) ---
                bool c0_bull = Open[0] < Close[0];
                bool c1_bull = Low[0] < Low[1];
                bool c2_bull = High[0] > High[1];
                bool c3_bull = Close[0] > High[1];

                if (c0_bull && c1_bull && c2_bull && c3_bull)
                {
                    isBullishSignal = true;
                }

                // Map OrderTypeSelection to NinjaTrader OrderType
                OrderType orderType;
                double orderPrice = Close[0]; // Default price for Market and Limit orders
                double stopPrice = 0; // Used for StopMarket and StopLimit

                switch (OrderTypeSelection)
                {
                    case "Market":
                        orderType = OrderType.Market;
                        break;
                    case "Limit":
                        orderType = OrderType.Limit;
                        break;
                    case "Stop":
                        orderType = OrderType.StopMarket;
                        // Set stop price slightly above/below Close[0] for buy/sell
                        stopPrice = isBearishSignal ? Close[0] + TickSize : Close[0] - TickSize;
                        break;
                    case "Stop Limit":
                        orderType = OrderType.StopLimit;
                        // Set stop price and limit price (limit price = Close[0], stop price offset by TickSize)
                        stopPrice = isBearishSignal ? Close[0] + TickSize : Close[0] - TickSize;
                        orderPrice = Close[0];
                        break;
                    default:
                        orderType = OrderType.Limit; // Fallback
                        break;
                }

                if (isBearishSignal) // Bearish pattern -> BUY
                {
                    isAtmStrategyCreated = false; // Reset ATM strategy created check
                    atmStrategyId = GetAtmStrategyUniqueId();
                    orderId = GetAtmStrategyUniqueId();
                    AtmStrategyCreate(OrderAction.Buy, orderType, orderPrice, stopPrice, TimeInForce.Day, orderId, AtmStrategyTemplate, atmStrategyId, (atmCallbackErrorCode, atmCallBackId) =>
                    {
                        if (atmCallbackErrorCode == ErrorCode.NoError && atmCallBackId == atmStrategyId)
                            isAtmStrategyCreated = true;
                        else
                            Print(string.Format("{0}: ATM Strategy Creation Failed. Error: {1}", Time[0], atmCallbackErrorCode));
                    });
                    Print(string.Format("{0}: Bearish Signal -> BUY. Order Type: {1}, Entry Price: {2}, Stop Price: {3}, Trade Size: {4}, ATM Template: {5}", 
                        Time[0], OrderTypeSelection, orderPrice, stopPrice, TradeSizeParameter, AtmStrategyTemplate));
                }
                else if (isBullishSignal) // Bullish pattern -> SELL
                {
                    isAtmStrategyCreated = false; // Reset ATM strategy created check
                    atmStrategyId = GetAtmStrategyUniqueId();
                    orderId = GetAtmStrategyUniqueId();
                    AtmStrategyCreate(OrderAction.Sell, orderType, orderPrice, stopPrice, TimeInForce.Day, orderId, AtmStrategyTemplate, atmStrategyId, (atmCallbackErrorCode, atmCallBackId) =>
                    {
                        if (atmCallbackErrorCode == ErrorCode.NoError && atmCallBackId == atmStrategyId)
                            isAtmStrategyCreated = true;
                        else
                            Print(string.Format("{0}: ATM Strategy Creation Failed. Error: {1}", Time[0], atmCallbackErrorCode));
                    });
                    Print(string.Format("{0}: Bullish Signal -> SELL. Order Type: {1}, Entry Price: {2}, Stop Price: {3}, Trade Size: {4}, ATM Template: {5}", 
                        Time[0], OrderTypeSelection, orderPrice, stopPrice, TradeSizeParameter, AtmStrategyTemplate));
                }
            }

            // Check that ATM strategy was created before checking other properties
            if (!isAtmStrategyCreated)
                return;

            // Check for a pending entry order
            if (orderId.Length > 0)
            {
                string[] status = GetAtmStrategyEntryOrderStatus(orderId);

                // If the status call can't find the order specified, the return array length will be zero
                if (status.GetLength(0) > 0)
                {
                    Print("The entry order average fill price is: " + status[0]);
                    Print("The entry order filled amount is: " + status[1]);
                    Print("The entry order order state is: " + status[2]);

                    // If the order state is terminal, reset the order id
                    if (status[2] == "Filled" || status[2] == "Cancelled" || status[2] == "Rejected")
                        orderId = string.Empty;
                }
            }
            // If the strategy has terminated, reset the strategy id
            else if (atmStrategyId.Length > 0 && GetAtmStrategyMarketPosition(atmStrategyId) == Cbi.MarketPosition.Flat)
                atmStrategyId = string.Empty;

            if (atmStrategyId.Length > 0)
            {
                // Print ATM strategy information
                Print("The current ATM Strategy market position is: " + GetAtmStrategyMarketPosition(atmStrategyId));
                Print("The current ATM Strategy position quantity is: " + GetAtmStrategyPositionQuantity(atmStrategyId));
                Print("The current ATM Strategy average price is: " + GetAtmStrategyPositionAveragePrice(atmStrategyId));
                Print("The current ATM Strategy Unrealized PnL is: " + GetAtmStrategyUnrealizedProfitLoss(atmStrategyId));
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Trade Size (Contracts/Lots)", Order=1, GroupName="Parameters")]
        public int TradeSizeParameter
        { get; set; }

        [NinjaScriptProperty]
        [TypeConverter(typeof(AtmStrategyConverter))]
        [PropertyEditor("NinjaTrader.Gui.Tools.StringStandardValuesEditorKey")]
        [Display(Name="ATM Strategy Template", Order=2, GroupName="Parameters")]
        public string AtmStrategyTemplate
        { get; set; }

        [NinjaScriptProperty]
        [TypeConverter(typeof(OrderTypeConverter))]
        [PropertyEditor("NinjaTrader.Gui.Tools.StringStandardValuesEditorKey")]
        [Display(Name="Order Type", Order=3, GroupName="Parameters")]
        public string OrderTypeSelection
        { get; set; }
        #endregion
    }
}