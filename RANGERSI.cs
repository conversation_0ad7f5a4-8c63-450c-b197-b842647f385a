#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public class RangeRSIStrategy_v4 : Strategy
    {
        #region Variables
        private RSI rsi; // RSI indicator instance
        private SMA fastMA; // Fast moving average for trend detection
        private SMA slowMA; // Slow moving average for trend detection

        private string atmStrategyId = string.Empty;
        private string orderId = string.Empty;
        private bool isAtmStrategyCreated = false;
        #endregion

        #region Parameters
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "RSI Period", Order = 1, GroupName = "RSI Parameters")]
        public int RSIPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "RSI Smooth Period", Order = 2, GroupName = "RSI Parameters")]
        public int RSISmooth { get; set; }

        [NinjaScriptProperty]
        [Range(0, 100)]
        [Display(Name = "Overbought Threshold", Order = 3, GroupName = "RSI Parameters")]
        public double OverboughtThreshold { get; set; }

        [NinjaScriptProperty]
        [Range(0, 100)]
        [Display(Name = "Oversold Threshold", Order = 4, GroupName = "RSI Parameters")]
        public double OversoldThreshold { get; set; }

        // Trend Filter Parameters
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Fast MA Period", Order = 1, GroupName = "Trend Filter")]
        public int FastMAPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Slow MA Period", Order = 2, GroupName = "Trend Filter")]
        public int SlowMAPeriod { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Enable Trend Filter", Order = 3, GroupName = "Trend Filter")]
        public bool EnableTrendFilter { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Only Trade With Trend", Order = 4, GroupName = "Trend Filter")]
        public bool OnlyTradeWithTrend { get; set; }

        // ATM Strategy Parameters
        [NinjaScriptProperty]
        [Display(Name = "ATM Strategy Template", Order = 1, GroupName = "ATM Strategy")]
        public string AtmStrategyTemplate { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Exit Existing Positions on Signal", Order = 2, GroupName = "ATM Strategy")]
        public bool ExitExistingPositions { get; set; }
        #endregion

        #region OnStateChange
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "RSI Strategy with bracket orders and trend filter for Range Chart.";
                Name = "RangeRSIStrategy_v4";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Day;
                TraceOrders = true;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;

                // Optimized RSI parameters for reduced drawdown
                RSIPeriod = 14;
                RSISmooth = 3;
                OverboughtThreshold = 65;
                OversoldThreshold = 35;

                // Default trend filter parameters
                FastMAPeriod = 20;
                SlowMAPeriod = 50;
                EnableTrendFilter = true;
                OnlyTradeWithTrend = true;

                // Conservative ATM Strategy parameters
                AtmStrategyTemplate = "ATM_VOLATILE";
                // Reduced position size for risk management
                DefaultQuantity = 2;
                ExitExistingPositions = true;

                // This strategy has been designed to take advantage of performance gains in Strategy Analyzer optimizations
                IsInstantiatedOnEachOptimizationIteration = false;
            }
            else if (State == State.Configure)
            {
                // Initialize RSI with user-specified period and smooth period
                rsi = RSI(Close, RSIPeriod, RSISmooth);
                AddChartIndicator(rsi);

                // Initialize moving averages for trend detection
                fastMA = SMA(Close, FastMAPeriod);
                slowMA = SMA(Close, SlowMAPeriod);
                AddChartIndicator(fastMA);
                AddChartIndicator(slowMA);
            }
        }
        #endregion

        #region OnBarUpdate
        protected override void OnBarUpdate()
        {
            try
            {
                // Make sure this strategy does not execute against historical data
                if (State == State.Historical)
                    return;

                // Calculate the minimum number of bars required
                int requiredBars = Math.Max(BarsRequiredToTrade,
                                   Math.Max(RSIPeriod + RSISmooth + 1,
                                   Math.Max(FastMAPeriod, SlowMAPeriod)));

                // Ensure we have enough bars
                if (CurrentBar < requiredBars)
                    return;

                // Check if indicators have valid data points
                if (!rsi.IsValidDataPoint(0) || !rsi.IsValidDataPoint(1) ||
                    !fastMA.IsValidDataPoint(0) || !slowMA.IsValidDataPoint(0))
                    return;

                // Get current and previous RSI values
                double rsiValue = rsi[0];
                double rsiPrev = rsi[1];

                // Determine market trend
                TrendDirection currentTrend = DetectTrend();

                // Log for debugging
                Print($"Bar {CurrentBar}: RSI={rsiValue}, Trend={currentTrend}, Fast MA={fastMA[0]}, Slow MA={slowMA[0]}");

                // Check for a pending entry order
                if (orderId.Length > 0)
                {
                    string[] status = GetAtmStrategyEntryOrderStatus(orderId);

                    // If the status call can't find the order specified, the return array length will be zero otherwise it will hold elements
                    if (status.GetLength(0) > 0)
                    {
                        // Print out some information about the order to the output window
                        Print("The entry order average fill price is: " + status[0]);
                        Print("The entry order filled amount is: " + status[1]);
                        Print("The entry order order state is: " + status[2]);

                        // If the order state is terminal, reset the order id value
                        if (status[2] == "Filled" || status[2] == "Cancelled" || status[2] == "Rejected")
                            orderId = string.Empty;
                    }
                }
                // If the strategy has terminated reset the strategy id
                else if (atmStrategyId.Length > 0 && GetAtmStrategyMarketPosition(atmStrategyId) == MarketPosition.Flat)
                    atmStrategyId = string.Empty;

                // If we have an active ATM Strategy, print information about it
                if (atmStrategyId.Length > 0)
                {
                    // Print some information about the strategy to the output window
                    Print("The current ATM Strategy market position is: " + GetAtmStrategyMarketPosition(atmStrategyId));
                    Print("The current ATM Strategy position quantity is: " + GetAtmStrategyPositionQuantity(atmStrategyId));
                    Print("The current ATM Strategy average price is: " + GetAtmStrategyPositionAveragePrice(atmStrategyId));
                    Print("The current ATM Strategy Unrealized PnL is: " + GetAtmStrategyUnrealizedProfitLoss(atmStrategyId));

                    // You can dynamically modify the stop price if needed
                    // AtmStrategyChangeStopTarget(0, Low[0] - 3 * TickSize, "STOP1", atmStrategyId);
                }

                // Only process entry signals if we're not already in a position
                if (orderId.Length == 0 && atmStrategyId.Length == 0)
                {
                    // LONG condition: RSI crosses below OversoldThreshold
                    if (rsiValue <= OversoldThreshold && rsiPrev > OversoldThreshold)
                    {
                        // Check trend filter if enabled
                        bool trendAllowsLong = !EnableTrendFilter ||
                                               (OnlyTradeWithTrend && currentTrend == TrendDirection.Up) ||
                                               (!OnlyTradeWithTrend && currentTrend != TrendDirection.Down);

                        if (trendAllowsLong)
                        {
                            Print($"Long signal triggered. RSI={rsiValue}, Trend={currentTrend}");

                            // Enter long with ATM Strategy
                            EnterLongWithBracketOrder();
                        }
                        else
                        {
                            Print($"Long signal ignored due to trend filter. RSI={rsiValue}, Trend={currentTrend}");
                        }
                    }

                    // SHORT condition: RSI crosses above OverboughtThreshold
                    else if (rsiValue >= OverboughtThreshold && rsiPrev < OverboughtThreshold)
                    {
                        // Check trend filter if enabled
                        bool trendAllowsShort = !EnableTrendFilter ||
                                                (OnlyTradeWithTrend && currentTrend == TrendDirection.Down) ||
                                                (!OnlyTradeWithTrend && currentTrend != TrendDirection.Up);

                        if (trendAllowsShort)
                        {
                            Print($"Short signal triggered. RSI={rsiValue}, Trend={currentTrend}");

                            // Enter short with ATM Strategy
                            EnterShortWithBracketOrder();
                        }
                        else
                        {
                            Print($"Short signal ignored due to trend filter. RSI={rsiValue}, Trend={currentTrend}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"Exception in OnBarUpdate: {ex.Message}");
                Print($"Stack Trace: {ex.StackTrace}");
            }
        }
        #endregion

        #region Helper Methods
        // Detect market trend using moving averages
        private TrendDirection DetectTrend()
        {
            // If fast MA is above slow MA, trend is up
            if (fastMA[0] > slowMA[0])
            {
                return TrendDirection.Up;
            }
            // If fast MA is below slow MA, trend is down
            else if (fastMA[0] < slowMA[0])
            {
                return TrendDirection.Down;
            }
            // If they're equal, market is sideways
            else
            {
                return TrendDirection.Sideways;
            }
        }

        // Enum for trend direction
        private enum TrendDirection
        {
            Up,
            Down,
            Sideways
        }
        #endregion

        #region Entry Methods
        private void EnterLongWithBracketOrder()
        {
            try
            {
                // Reset ATM strategy created check to false
                isAtmStrategyCreated = false;

                // Generate unique IDs for ATM strategy and order
                atmStrategyId = GetAtmStrategyUniqueId();
                orderId = GetAtmStrategyUniqueId();

                // Create ATM strategy for long entry
                AtmStrategyCreate(OrderAction.Buy, OrderType.Market, Close[0], 0, TimeInForce.Day,
                    orderId, AtmStrategyTemplate, atmStrategyId, (atmCallbackErrorCode, atmCallBackId) =>
                {
                    // Check that the ATM strategy create did not result in error, and that the requested ATM strategy matches the id in callback
                    if (atmCallbackErrorCode == ErrorCode.NoError && atmCallBackId == atmStrategyId)
                    {
                        isAtmStrategyCreated = true;
                        Print($"ATM Strategy created successfully for LONG entry using template: {AtmStrategyTemplate}");
                    }
                    else
                    {
                        Print($"Error creating ATM Strategy for LONG entry: {atmCallbackErrorCode}");
                    }
                });
            }
            catch (Exception ex)
            {
                Print($"Error entering long position: {ex.Message}");
            }
        }

        private void EnterShortWithBracketOrder()
        {
            try
            {
                // Reset ATM strategy created check to false
                isAtmStrategyCreated = false;

                // Generate unique IDs for ATM strategy and order
                atmStrategyId = GetAtmStrategyUniqueId();
                orderId = GetAtmStrategyUniqueId();

                // Create ATM strategy for short entry
                AtmStrategyCreate(OrderAction.Sell, OrderType.Market, Close[0], 0, TimeInForce.Day,
                    orderId, AtmStrategyTemplate, atmStrategyId, (atmCallbackErrorCode, atmCallBackId) =>
                {
                    // Check that the ATM strategy create did not result in error, and that the requested ATM strategy matches the id in callback
                    if (atmCallbackErrorCode == ErrorCode.NoError && atmCallBackId == atmStrategyId)
                    {
                        isAtmStrategyCreated = true;
                        Print($"ATM Strategy created successfully for SHORT entry using template: {AtmStrategyTemplate}");
                    }
                    else
                    {
                        Print($"Error creating ATM Strategy for SHORT entry: {atmCallbackErrorCode}");
                    }
                });
            }
            catch (Exception ex)
            {
                Print($"Error entering short position: {ex.Message}");
            }
        }
        #endregion

        #region Order Event Handlers
        protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string nativeError)
        {
            try
            {
                // Log all order updates for debugging
                Print($"Order Update: Name={order.Name}, State={orderState}, Quantity={quantity}, Filled={filled}, AvgFillPrice={averageFillPrice}, Error={error}, NativeError={nativeError}");

                if (error != ErrorCode.NoError && orderState == OrderState.Rejected)
                {
                    Print($"Order Rejected: Name={order.Name}, Error={error}, NativeError={nativeError}");

                    // If a stop or limit order was rejected, can implement recovery logic here
                }
            }
            catch (Exception ex)
            {
                Print($"Exception in OnOrderUpdate: {ex.Message}");
            }
        }

        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            try
            {
                Print($"Execution Update: ID={executionId}, Price={price}, Quantity={quantity}, Position={marketPosition}, OrderID={orderId}");
            }
            catch (Exception ex)
            {
                Print($"Exception in OnExecutionUpdate: {ex.Message}");
            }
        }
        #endregion
    }
}